# Advanced PowerShell script to fix BOM and encoding issues in Java files

$sourceDir = "src\main\java"

# Get all Java files recursively
$javaFiles = Get-ChildItem -Path $sourceDir -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    Write-Host "Processing: $($file.Name)"
    
    try {
        # Read the file content as UTF-8 without BOM
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        
        # Remove BOM characters if present
        if ($content.StartsWith([char]0xFEFF)) {
            $content = $content.Substring(1)
            Write-Host "    Removed BOM character"
        }

        # Remove any remaining invisible characters at the beginning
        $content = $content.TrimStart([char]0xFEFF, [char]0xEF, [char]0xBB, [char]0xBF)
        
        # Write back as UTF-8 without BOM
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBom)
        
        Write-Host "  Fixed encoding for: $($file.Name)"
    }
    catch {
        Write-Host "  Error processing $($file.Name): $($_.Exception.Message)"
    }
}

Write-Host "Advanced BOM fix completed!"
