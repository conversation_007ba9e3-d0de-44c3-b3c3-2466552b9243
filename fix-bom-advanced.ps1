# Advanced PowerShell script to fix BOM and encoding issues in Java files

$sourceDir = "src\main\java"

# Get all Java files recursively
$javaFiles = Get-ChildItem -Path $sourceDir -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    Write-Host "Processing: $($file.Name)"
    
    try {
        # Read the file content as UTF-8 without BOM
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        
        # Remove BOM characters if present
        $content = $content -replace "^\ufeff", ""
        $content = $content -replace "^\uefbb\ubf", ""
        
        # Remove multiple consecutive BOM-like characters
        $content = $content -replace "^[\ufeff\uefbb\ubf]+", ""
        
        # Write back as UTF-8 without BOM
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBom)
        
        Write-Host "  Fixed encoding for: $($file.Name)"
    }
    catch {
        Write-Host "  Error processing $($file.Name): $($_.Exception.Message)"
    }
}

Write-Host "Advanced BOM fix completed!"
