# PowerShell script to fix package declarations in decompiled Java files

$sourceDir = "src\main\java\com\aaron\authserver"

# Get all Java files recursively
$javaFiles = Get-ChildItem -Path $sourceDir -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    Write-Host "Processing: $($file.FullName)"
    
    # Read the file content
    $content = Get-Content $file.FullName -Raw
    
    # Skip if already processed (has correct package declaration)
    if ($content -match "^package com\.aaron\.authserver") {
        Write-Host "  Already has correct package declaration, skipping..."
        continue
    }
    
    # Determine the correct package based on file path
    $relativePath = $file.DirectoryName.Replace((Get-Location).Path + "\$sourceDir", "").Replace("\", ".")
    $correctPackage = "com.aaron.authserver" + $relativePath
    
    # Fix package declaration
    $content = $content -replace "package BOOT-INF\.classes\.com\.aaron\.authserver[^;]*;", "package $correctPackage;"
    $content = $content -replace "/\*[^*]*\*/ package BOOT-INF\.classes\.com\.aaron\.authserver[^;]*;", "package $correctPackage;"
    
    # Remove JD-Core comments at the end
    $content = $content -replace "/\*[^*]*Location:[^*]*\*/", ""
    
    # Remove line number comments
    $content = $content -replace "/\*\s*\d+\s*\*/", ""
    
    # Write back to file
    Set-Content -Path $file.FullName -Value $content -Encoding UTF8
    
    Write-Host "  Fixed package declaration to: $correctPackage"
}

Write-Host "Package declaration fix completed!"
