@echo off
echo Starting JD-G<PERSON> to decompile class files...
echo.
echo Instructions:
echo 1. JD-GUI will open shortly
echo 2. Open the BOOT-INF/classes folder in JD-GUI
echo 3. Navigate to com/aaron/authserver
echo 4. Select all Java classes and save them to the auth-server-source/src/main/java directory
echo 5. Make sure to maintain the package structure
echo.
echo JD-GUI Path: D:\DEV\JAVA\jd-gui-0.3.5.windows
echo Target Directory: %CD%\auth-server-source\src\main\java
echo.
pause
start "" "D:\DEV\JAVA\jd-gui-0.3.5.windows\jd-gui.exe"
