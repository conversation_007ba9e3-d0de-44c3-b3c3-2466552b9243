# Final PowerShell script to completely fix all Java files

$sourceDir = "src\main\java"

# Get all Java files recursively
$javaFiles = Get-ChildItem -Path $sourceDir -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    Write-Host "Processing: $($file.Name)"
    
    try {
        # Read the file as bytes first to handle BOM properly
        $bytes = [System.IO.File]::ReadAllBytes($file.FullName)
        
        # Remove BOM if present (EF BB BF)
        if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
            $bytes = $bytes[3..($bytes.Length-1)]
            Write-Host "  Removed BOM"
        }
        
        # Convert to string
        $content = [System.Text.Encoding]::UTF8.GetString($bytes)
        
        # Remove any remaining BOM characters
        $content = $content.TrimStart([char]0xFEFF)
        
        # Fix truncated keywords - be more aggressive
        $content = $content -replace "^[^a-zA-Z]*ackage\s", "package "
        $content = $content -replace "^[^a-zA-Z]*mport\s", "import "
        $content = $content -replace "^[^a-zA-Z]*ublic\s", "public "
        $content = $content -replace "^[^a-zA-Z]*rivate\s", "private "
        $content = $content -replace "^[^a-zA-Z]*rotected\s", "protected "
        $content = $content -replace "^[^a-zA-Z]*lass\s", "class "
        $content = $content -replace "^[^a-zA-Z]*nterface\s", "interface "
        $content = $content -replace "^[^a-zA-Z]*num\s", "enum "
        
        # Fix malformed first lines with comments
        $content = $content -replace "^\*+\s*\*/\s*package\s", "package "
        $content = $content -replace "^\*/\s*package\s", "package "
        $content = $content -replace "^\*+\s*package\s", "package "
        
        # Ensure the file starts with a proper keyword
        if (-not ($content -match "^(package|import|public|private|protected|class|interface|enum|@|\s*//|\s*/\*)")) {
            # Try to find the first valid line
            $lines = $content -split "`n"
            $validLineIndex = -1
            for ($i = 0; $i -lt $lines.Length; $i++) {
                if ($lines[$i] -match "^\s*(package|import|public|private|protected|class|interface|enum|@)") {
                    $validLineIndex = $i
                    break
                }
            }
            
            if ($validLineIndex -gt 0) {
                $content = ($lines[$validLineIndex..($lines.Length-1)] -join "`n")
                Write-Host "  Removed $validLineIndex invalid lines from beginning"
            }
        }
        
        # Write back as UTF-8 without BOM
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBom)
        
        Write-Host "  Successfully fixed: $($file.Name)"
    }
    catch {
        Write-Host "  Error processing $($file.Name): $($_.Exception.Message)"
    }
}

Write-Host "Final fix completed!"
