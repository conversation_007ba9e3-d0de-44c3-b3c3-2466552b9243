body {
  background-position: center;
  background-color: #ffffff;
  background-repeat: no-repeat;
  background-size: cover;
  color: #1B1B1B;
  font-family: "PingFang SC","Noto Sans CJK","Source Han Sans","Microsoft Yahei",source-han-sans-simplified-c,sans-serif;
  font-size: 14px;
  font-weight: normal;
  text-transform: none;
  line-height: 1.5;
}
.login-page {
  width: auto;
  margin: auto;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  /* min-height: 100%; */
  min-height: calc(100vh - 60px);
  /* padding: 10px; */
}
.logo img {
  width: 200px;
  margin-bottom: 60px;
}
.form {
  position: relative;
  z-index: 1;
  background: #FFFFFF;
  max-width: 100%;
  width: 360px;
  padding: 35px;
  text-align: center;
  /* box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2), 0 5px 5px 0 rgba(0, 0, 0, 0.24); */
  box-shadow: 0 1px 30px 0 rgba(0,0,0,0.1);
  border-radius: 6px;
}
.form input {
  font-family: "Roboto", sans-serif;
  outline: 0;
  background: #f2f2f2;
  width: 100%;
  border: 0;
  margin: 0 0 15px;
  padding: 15px;
  box-sizing: border-box;
  font-size: 14px;
}
.form .button {
  font-family: "Roboto", sans-serif;
  text-transform: uppercase;
  outline: 0;
  background: #C9232D;
  width: 100%;
  border: 0;
  padding: 15px;
  color: #FFFFFF;
  font-size: 14px;
  -webkit-transition: all 0.3 ease;
  transition: all 0.3 ease;
  cursor: pointer;
}
.form button:hover,.form button:active,.form button:focus {
  background: #C9232D;
}
.form .message {
  margin: 15px 0 0;
  color: #b3b3b3;
  font-size: 12px;
}
.form .error {
	color: #C9232D;
}
.form h1 {
	color: #1B1B1B;
  display: block;
  font-size: 1.6em;
  line-height: 1.5cap;
}
.form .message a {
  color: #C9232D;
  text-decoration: none;
}
body {
  background: #FFFFFF;
  font-family: "Roboto", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.footer-copyright {
  margin: 0;
  font-size: 14px;
  font-weight: normal;
}

footer {
  text-align: center;
  width: auto;
  height: 60px;
}
