
server:
  port: 9000

logging:
  level:
    org:
      springframework:
        security: INFO

spring:
  application:
    name: authserver
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: ************************************************************************************************************************************************************
    username: root
    password: hello123
  security:
    oauth2:
      authorizationserver:
        client:
          client:
            registration:
              client-id: "client"
              client-name: "恩道书房"
              client-secret: "{noop}secret"
              client-authentication-methods:
                - "client_secret_basic"
              authorization-grant-types:
                - "authorization_code"
                - "refresh_token"
                - "client_credentials"
              redirect-uris:
                - "http://**************" #http://************** #http://**************/login/oauth2/code/oidc-client
                - "http://**************/?auth=sso"
              post-logout-redirect-uris:
                - "http://test" #"http://127.0.0.1:8080/logged-out"
              scopes:
                - "openid"
                - "profile"
                - "user.read"
                - "user.write"
            require-authorization-consent: true
            require-proof-key: true

security:
  jwt:
    token:
      secret-key: secret-key
      expire-length: 600000 # 5 minutes duration by default: 5 minutes * 60 seconds * 1000 miliseconds

UserController:
  signin: Authenticates user and returns its JWT token.
  signup: Creates user and returns its JWT token
  delete: Deletes specific user by username
  search: Returns specific user by username
  me: Returns current user's data