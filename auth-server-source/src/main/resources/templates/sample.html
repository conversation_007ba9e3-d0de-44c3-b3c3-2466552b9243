<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
	<head>
		<title>Please Log In</title>
		<link rel="stylesheet" href="/css/style.css" th:href="@{/css/style.css}">
	</head>
	<body>
		<div class="login-page">
		  	<div class="form">
				<h1>用户登录认证</h1>
				<h2>Please Log In</h2>
				<div class="error" th:if="${param.error}">
					Invalid username and password.</div>
				<div class="error" th:if="${param.logout}">
					You have been logged out.</div>
				<form  class="login-form" th:action="@{/login}" method="post">
					<div>
					<input type="text" name="username" placeholder="Username"/>
					</div>
					<div>
					<input type="password" name="password" placeholder="Password"/>
					</div>
					<input class="button" type="submit" value="Log in" />
				</form>
				<p>Inspirata Publishing (Hong Kong) Limited</p>
			</div>
		</div>
<!--
public class Teacher implements Serializable {
    private String gender;
    private boolean isActive;
    private List<String> courses = new ArrayList<>();
    private String additionalSkills;
-->
		<table>
			<tr>
				<td th:text="${teacher.additionalSkills} ?: 'UNKNOWN'" />
				<td th:text="${teacher.active} ? 'ACTIVE' : 'RETIRED'" />
			</tr>
			<tr>
				<td colspan="2">
					<span th:if="${teacher.gender == 'F'}">Female</span>
					<span th:unless="${teacher.gender == 'F'}">Male</span>
				</td>
			</tr>
			<tr>
				<td th:switch="${#lists.size(teacher.courses)}" colspan="2">
					<span th:case="'0'">NO COURSES YET!</span>
					<span th:case="'1'" th:text="${teacher.courses[0]}"></span>
					<div th:case="*">
						<div th:each="course:${teacher.courses}" th:text="${course}"/>
					</div>
				</td>
			</tr>
		</table>
	</body>
</html>