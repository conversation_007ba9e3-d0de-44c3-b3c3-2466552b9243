<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
	<head>
		<title th:text="|#{login.title}-#{main.title}|">账号登录丨恩道电子书</title>
		<!-- SEO Meta Tags-->
		<meta name="description" content="恩道电子书">
		<meta name="keywords" content="恩道电子书">
		<!-- Mobile Specific Meta Tag-->
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
		<!-- Favicon Icons-->
		<link rel="icon" type="image/x-icon" href="/images/favicon.ico" th:href="@{/images/favicon.ico}">
		<link rel="icon" type="image/png" href="/images/favicon.png" th:href="@{/images/favicon.png}">
		<link rel="stylesheet" href="/css/style.css" th:href="@{/css/style.css}">
	</head>
	<body>
	<div class="login-page">
		<div class="logo">
			<img src="/images/logo.png" alt="Logo">
		</div>
		<div class="form" id="login-form">
			<h1 id="login-title" th:text="#{login.title}">Please Log In</h1>
			<div class="error" id="invalid-account" th:if="${param.error}" th:text="#{login.error.invalid}">
				Invalid username and password.</div>
			<div class="error" id="log-out" th:if="${param.logout}" th:text="#{login.error.logout}">
				You have been logged out.</div>
			<form class="login-form" th:action="@{/login}" method="post">
				<div>
					<input type="text" name="username" id="username" th:placeholder="#{login.placeholder.name}"/>
				</div>
				<div>
					<input type="password" name="password" id="password" th:placeholder="#{login.placeholder.password}"/>
				</div><br>
				<input class="button" type="submit" th:value="#{login.button}">
			</form>
		</div>
	</div>
	<footer class="site-footer">
		<p class="footer-copyright" th:text="#{main.copyright}">Copyright © 2025 Inspirata Publishing (Hong Kong) Limited</p>
	</footer>
<!--	<script src="/js/script.js"></script>-->
	<!--
		<div class="login-page">
		  	<div class="form">
				<h1 th:text="#{login.title}">用户登录认证</h1>
				<h2>Please Log In</h2>
				<div class="error" th:if="${param.error}">
					Invalid username and password.</div>
				<div class="error" th:if="${param.logout}">
					You have been logged out.</div>
				<form  class="login-form" th:action="@{/login}" method="post">
					<div>
					<input type="text" name="username" placeholder="Username"/>
					</div>
					<div>
					<input type="password" name="password" placeholder="Password"/>
					</div>
					<input class="button" type="submit" value="Log in" th:value="#{login.title}"/>
				</form>
				<p th:text="#{main.copyright}">Inspirata Publishing (Hong Kong) Limited</p>
			</div>
		</div>
		-->
	</body>
</html>