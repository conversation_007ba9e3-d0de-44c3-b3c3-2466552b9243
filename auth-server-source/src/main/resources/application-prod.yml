
server:
  port: 9000

logging:
  level:
    org:
      springframework:
        security: INFO

spring:
  application:
    name: authserver
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: ****************************************************************************************************************************************************************************************************************************
    username: endao_bookstore
    password: sxBPQMFnx2U@e8
  security:
    oauth2:
      authorizationserver:
        client:
          client:
            registration:
              client-id: "client"
              client-name: "恩道书房"
              client-secret: "8e7d8418f3f446db31d4612fb14692076ac1ed7b"
              client-authentication-methods:
                - "client_secret_basic"
              authorization-grant-types:
                - "authorization_code"
                - "refresh_token"
                - "client_credentials"
              redirect-uris:
                - "http://20.189.113.131" #http://20.189.113.131 #http://20.189.113.131/login/oauth2/code/oidc-client
                - "http://20.189.113.131/?auth=sso"
                - "https://book.endao.co/?auth=sso"
                - "https://betabook.endao.co/?auth=sso"
              post-logout-redirect-uris:
                - "http://test" #"http://127.0.0.1:8080/logged-out"
              scopes:
                - "openid"
                - "profile"
                - "user.read"
                - "user.write"
            require-authorization-consent: true
            require-proof-key: true

security:
  jwt:
    token:
      secret-key: secret-key
      expire-length: 600000 # 5 minutes duration by default: 5 minutes * 60 seconds * 1000 miliseconds

UserController:
  signin: Authenticates user and returns its JWT token.
  signup: Creates user and returns its JWT token
  delete: Deletes specific user by username
  search: Returns specific user by username
  me: Returns current user's data