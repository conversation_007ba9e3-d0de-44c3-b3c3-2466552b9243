/*     */ package com.aaron.authserver.service;
/*     */ 
/*     */ import com.aaron.authserver.model.IRole;
/*     */ import com.aaron.authserver.model.ITable;
/*     */ import com.aaron.authserver.model.IUser;
/*     */ import com.aaron.authserver.model.User;
/*     */ import com.aaron.authserver.service.UserService;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.List;
/*     */ import java.util.Set;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ class null
/*     */   implements IUser
/*     */ {
/*     */   public Object getUserId() {
     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Collection<IRole> getAuthorities() {
     Object object = new Object(this);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
     List<IRole> roleList = new ArrayList<>();
     roleList.add(object);
/*     */     
     return roleList;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAuthorities(Iterable<? extends IRole> var1) {}
/*     */ 
/*     */ 
/*     */   
/*     */   public Set<String> getHasPermissionUrlPatterns() {
     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Collection<ITable> getPermissionTables() {
     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object getSalt() {
     return user.salt();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getEmail() {
     return user.email();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getNickName() {
     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object getCustomDetail() {
     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isActived() {
     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object getUserInfo() {
     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getPassword() {
     return user.password();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getUsername() {
     return user.email();
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isAccountNonExpired() {
     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isAccountNonLocked() {
     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isCredentialsNonExpired() {
     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isEnabled() {
     return user.enabled().booleanValue();
/*     */   }
/*     */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\service\UserService$1.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
