/*     */ package BOOT-INF.classes.com.aaron.authserver.service;
/*     */ 
/*     */ import com.aaron.authserver.model.IRole;
/*     */ import com.aaron.authserver.model.ITable;
/*     */ import com.aaron.authserver.model.IUser;
/*     */ import com.aaron.authserver.model.User;
/*     */ import com.aaron.authserver.service.UserService;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.List;
/*     */ import java.util.Set;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ class null
/*     */   implements IUser
/*     */ {
/*     */   public Object getUserId() {
/* 200 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Collection<IRole> getAuthorities() {
/* 205 */     Object object = new Object(this);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 233 */     List<IRole> roleList = new ArrayList<>();
/* 234 */     roleList.add(object);
/*     */     
/* 236 */     return roleList;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAuthorities(Iterable<? extends IRole> var1) {}
/*     */ 
/*     */ 
/*     */   
/*     */   public Set<String> getHasPermissionUrlPatterns() {
/* 246 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Collection<ITable> getPermissionTables() {
/* 251 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object getSalt() {
/* 256 */     return user.salt();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getEmail() {
/* 261 */     return user.email();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getNickName() {
/* 266 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object getCustomDetail() {
/* 271 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isActived() {
/* 276 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object getUserInfo() {
/* 281 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getPassword() {
/* 286 */     return user.password();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getUsername() {
/* 291 */     return user.email();
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isAccountNonExpired() {
/* 296 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isAccountNonLocked() {
/* 301 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isCredentialsNonExpired() {
/* 306 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isEnabled() {
/* 311 */     return user.enabled().booleanValue();
/*     */   }
/*     */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\service\UserService$1.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */