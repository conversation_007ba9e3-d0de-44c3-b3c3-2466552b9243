/*    */ package BOOT-INF.classes.com.aaron.authserver.service;
/*    */ 
/*    */ import com.aaron.authserver.service.UserService;
/*    */ import java.util.function.Supplier;
/*    */ import org.springframework.beans.factory.aot.AutowiredArguments;
/*    */ import org.springframework.beans.factory.aot.BeanInstanceSupplier;
/*    */ import org.springframework.beans.factory.config.BeanDefinition;
/*    */ import org.springframework.beans.factory.support.RegisteredBean;
/*    */ import org.springframework.beans.factory.support.RootBeanDefinition;
/*    */ import org.springframework.jdbc.core.simple.JdbcClient;
/*    */ 
/*    */ 
/*    */ public class UserService__BeanDefinitions
/*    */ {
/*    */   private static BeanInstanceSupplier<UserService> getUserServiceInstanceSupplier() {
/* 16 */     return BeanInstanceSupplier.forConstructor(new Class[] { JdbcClient.class
/* 17 */         }).withGenerator((paramRegisteredBean, paramAutowiredArguments) -> new UserService((JdbcClient)paramAutowiredArguments.get(0)));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static BeanDefinition getUserServiceBeanDefinition() {
/* 24 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(UserService.class);
/* 25 */     rootBeanDefinition.setInstanceSupplier((Supplier)getUserServiceInstanceSupplier());
/* 26 */     return (BeanDefinition)rootBeanDefinition;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\service\UserService__BeanDefinitions.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */