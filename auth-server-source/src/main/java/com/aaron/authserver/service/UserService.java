/*     */ package BOOT-INF.classes.com.aaron.authserver.service;
/*     */ 
/*     */ import com.aaron.authserver.mapper.AuthoritiesResultExtractor;
/*     */ import com.aaron.authserver.mapper.UserResultExtractor;
/*     */ import com.aaron.authserver.mapper.UserRowMapper;
/*     */ import com.aaron.authserver.mapper.UserTestRowMapper;
/*     */ import com.aaron.authserver.model.Authorities;
/*     */ import com.aaron.authserver.model.IUser;
/*     */ import com.aaron.authserver.model.User;
/*     */ import com.aaron.authserver.model.UserTest;
/*     */ import com.aaron.authserver.model.User_Authority;
/*     */ import io.micrometer.common.util.StringUtils;
/*     */ import java.util.Collections;
/*     */ import java.util.Date;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Optional;
/*     */ import org.springframework.jdbc.core.ResultSetExtractor;
/*     */ import org.springframework.jdbc.core.RowMapper;
/*     */ import org.springframework.jdbc.core.simple.JdbcClient;
/*     */ import org.springframework.jdbc.support.GeneratedKeyHolder;
/*     */ import org.springframework.security.core.userdetails.UserDetails;
/*     */ import org.springframework.security.core.userdetails.UserDetailsService;
/*     */ import org.springframework.security.core.userdetails.UsernameNotFoundException;
/*     */ import org.springframework.security.oauth2.core.oidc.OidcUserInfo;
/*     */ import org.springframework.stereotype.Service;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Service
/*     */ public class UserService
/*     */   implements UserDetailsService
/*     */ {
/*     */   private static final String SQL_FIND_ALL_USERS = "SELECT oc.firstname, oc.lastname, oc.email,oc.password,oc.salt, oc.approved FROM oc_customer oc;\n";
/*     */   private static final String SQL_FIND_ALL_USERS_TEST = "SELECT users.username, users.password, users.enabled, users_custom_info.test\nFROM users_custom_info, users\nWHERE users.username = users_custom_info.username;\n";
/*     */   private static final String SQL_FIND_ALL_USERS_Authorities = "SELECT users.username, users.password, users.enabled, authorities.authority, users_custom_info.test\nFROM users_custom_info, users\nLEFT JOIN authorities ON users.username = authorities.username\nWHERE users.username = users_custom_info.username;\n";
/*     */   private static final String SQL_FIND_ALL_AUTHORITIES = "SELECT authorities.authority, users.username, users.password, users.enabled, users_custom_info.test\nFROM users_custom_info, authorities\nLEFT JOIN users  ON users.username = authorities.username\nWHERE users.username = users_custom_info.username;\n";
/*     */   private static final String SQL_FIND_BY_USERNAME = "SELECT oc.firstname, oc.lastname, oc.email, oc.password, oc.salt, oc.approved\nFROM oc_customer oc\nWHERE oc.email = :username;\n";
/*     */   private static final String SQL_FIND_BY_EMAIL = "SELECT oc.firstname, oc.lastname, oc.email, oc.password, oc.salt, oc.approved\nFROM oc_customer oc\nWHERE oc.email = :email;\n";
/*     */   private static final String SQL_CREATE_USER = "INSERT INTO oc_customer(firstname,lastname, password, approved)\nVALUES(?, ?, ?);\n";
/*     */   private static final String SQL_UPDATE_USER = "UPDATE oc_customer SET password = ?, approved = ? WHERE email = ?;\n";
/*     */   private static final String SQL_DELETE_USER = "DELETE FROM oc_customer WHERE email = ?;\n";
/*     */   private final JdbcClient jdbcClient;
/*     */   
/*     */   public UserService(JdbcClient jdbcClient) {
/*  75 */     this.jdbcClient = jdbcClient;
/*     */   }
/*     */   
/*     */   public List<User> findAllUsers() {
/*  79 */     return this.jdbcClient.sql("SELECT oc.firstname, oc.lastname, oc.email,oc.password,oc.salt, oc.approved FROM oc_customer oc;\n")
/*  80 */       .query(User.class)
/*  81 */       .list();
/*     */   }
/*     */   
/*     */   public List<User> findAllUsersRowMapper() {
/*  85 */     return this.jdbcClient.sql("SELECT oc.firstname, oc.lastname, oc.email,oc.password,oc.salt, oc.approved FROM oc_customer oc;\n")
/*  86 */       .query((RowMapper)new UserRowMapper())
/*  87 */       .list();
/*     */   }
/*     */   
/*     */   public List<UserTest> findAllUsersTest() {
/*  91 */     return this.jdbcClient.sql("SELECT users.username, users.password, users.enabled, users_custom_info.test\nFROM users_custom_info, users\nWHERE users.username = users_custom_info.username;\n")
/*  92 */       .query(UserTest.class)
/*  93 */       .list();
/*     */   }
/*     */   
/*     */   public List<UserTest> findAllUsersTestRowMapper() {
/*  97 */     return this.jdbcClient.sql("SELECT users.username, users.password, users.enabled, users_custom_info.test\nFROM users_custom_info, users\nWHERE users.username = users_custom_info.username;\n")
/*  98 */       .query((RowMapper)new UserTestRowMapper())
/*  99 */       .list();
/*     */   }
/*     */   
/*     */   public List<User_Authority> findAllUsersAuthorities() {
/* 103 */     return (List<User_Authority>)this.jdbcClient.sql("SELECT users.username, users.password, users.enabled, authorities.authority, users_custom_info.test\nFROM users_custom_info, users\nLEFT JOIN authorities ON users.username = authorities.username\nWHERE users.username = users_custom_info.username;\n")
/* 104 */       .query((ResultSetExtractor)new UserResultExtractor());
/*     */   }
/*     */   
/*     */   public List<Authorities> findAllAuthoritiesUsers() {
/* 108 */     return (List<Authorities>)this.jdbcClient.sql("SELECT authorities.authority, users.username, users.password, users.enabled, users_custom_info.test\nFROM users_custom_info, authorities\nLEFT JOIN users  ON users.username = authorities.username\nWHERE users.username = users_custom_info.username;\n")
/* 109 */       .query((ResultSetExtractor)new AuthoritiesResultExtractor());
/*     */   }
/*     */   
/*     */   public Optional<User> findByUsername(String username) {
/* 113 */     return this.jdbcClient.sql("SELECT oc.firstname, oc.lastname, oc.email, oc.password, oc.salt, oc.approved\nFROM oc_customer oc\nWHERE oc.email = :username;\n")
/* 114 */       .param("username", username)
/* 115 */       .query((RowMapper)new UserRowMapper())
/* 116 */       .optional();
/*     */   }
/*     */   
/*     */   public Optional<User> findUserByEmail(String email) {
/* 120 */     return this.jdbcClient.sql("SELECT oc.firstname, oc.lastname, oc.email, oc.password, oc.salt, oc.approved\nFROM oc_customer oc\nWHERE oc.email = :email;\n")
/* 121 */       .param("email", email)
/* 122 */       .query((RowMapper)new UserRowMapper())
/* 123 */       .optional();
/*     */   }
/*     */   
/*     */   public Long createUser(User user) {
/* 127 */     GeneratedKeyHolder generatedKeyHolder = new GeneratedKeyHolder();
/* 128 */     this.jdbcClient.sql("INSERT INTO oc_customer(firstname,lastname, password, approved)\nVALUES(?, ?, ?);\n")
/* 129 */       .params(new Object[] { user.username(), user.password(), user.enabled()
/* 130 */         }).update();
/* 131 */     return (Long)generatedKeyHolder.getKeyAs(Long.class);
/*     */   }
/*     */   
/*     */   public void updateUser(User user) {
/* 135 */     Integer updated = Integer.valueOf(this.jdbcClient.sql("UPDATE oc_customer SET password = ?, approved = ? WHERE email = ?;\n")
/* 136 */         .params(new Object[] { user.password(), user.enabled(), user.username()
/* 137 */           }).update());
/* 138 */     if (updated.intValue() == 0) {
/* 139 */       throw new RuntimeException("User not found");
/*     */     }
/*     */   }
/*     */   
/*     */   public void deleteUser(String username) {
/* 144 */     Integer updated = Integer.valueOf(this.jdbcClient.sql("DELETE FROM oc_customer WHERE email = ?;\n")
/* 145 */         .params(new Object[] { username
/* 146 */           }).update());
/* 147 */     if (updated.intValue() == 0) {
/* 148 */       throw new RuntimeException("User not found");
/*     */     }
/*     */   }
/*     */   
/*     */   public OidcUserInfo getOidcUserInfoByEmail(String email) {
/* 153 */     Optional<User> userOptional = findUserByEmail(email);
/* 154 */     if (userOptional.isEmpty()) {
/* 155 */       return null;
/*     */     }
/* 157 */     User user = userOptional.get();
/* 158 */     return createUserInfoByUser(user);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public OidcUserInfo createUserInfoByUser(User user) {
/* 183 */     Map<String, Object> claimsImport = OidcUserInfo.builder().subject(user.email()).name(user.username()).givenName(user.firstName()).familyName(user.lastName()).middleName("Middle").nickname(user.username()).preferredUsername(user.username()).profile("https://example.com/" + user.username()).picture("https://example.com/" + user.username() + ".jpg").website("https://example.com").email(user.email()).emailVerified(Boolean.valueOf(true)).gender("female").birthdate("1970-01-01").zoneinfo("Europe/Paris").locale("en-US").phoneNumber("+****************;ext=5678").phoneNumberVerified(Boolean.valueOf(false)).claim("address", Collections.singletonMap("formatted", "Champ de Mars\n5 Av. Anatole France\n75007 Paris\nFrance")).updatedAt((new Date()).toString()).build().getClaims();
/* 184 */     return new OidcUserInfo(claimsImport);
/*     */   }
/*     */ 
/*     */   
/*     */   public IUser loadUserByUsername(String username) throws UsernameNotFoundException {
/* 189 */     if (StringUtils.isBlank(username)) {
/* 190 */       return null;
/*     */     }
/* 192 */     Optional<User> userOptional = findUserByEmail(username);
/* 193 */     if (userOptional.isEmpty() == true) {
/* 194 */       return null;
/*     */     }
/* 196 */     User user = userOptional.get();
/* 197 */     return (IUser)new Object(this, user);
/*     */   }
/*     */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\service\UserService.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */