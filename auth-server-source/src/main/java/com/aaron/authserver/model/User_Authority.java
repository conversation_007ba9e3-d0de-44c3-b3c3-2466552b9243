/*   */ package BOOT-INF.classes.com.aaron.authserver.model;public final class User_Authority extends Record { private final String username; private final String password; private final Boolean enabled;
/*   */   private final String test;
/*   */   private final List<String> authority;
/*   */   
/* 5 */   public User_Authority(String username, String password, Boolean enabled, String test, List<String> authority) { this.username = username; this.password = password; this.enabled = enabled; this.test = test; this.authority = authority; } public final String toString() { // Byte code:
/*   */     //   0: aload_0
/*   */     //   1: <illegal opcode> toString : (Lcom/aaron/authserver/model/User_Authority;)Ljava/lang/String;
/*   */     //   6: areturn
/*   */     // Line number table:
/*   */     //   Java source line number -> byte code offset
/*   */     //   #5	-> 0
/*   */     // Local variable table:
/*   */     //   start	length	slot	name	descriptor
/* 5 */     //   0	7	0	this	Lcom/aaron/authserver/model/User_Authority; } public String username() { return this.username; } public final int hashCode() { // Byte code:
/*   */     //   0: aload_0
/*   */     //   1: <illegal opcode> hashCode : (Lcom/aaron/authserver/model/User_Authority;)I
/*   */     //   6: ireturn
/*   */     // Line number table:
/*   */     //   Java source line number -> byte code offset
/*   */     //   #5	-> 0
/*   */     // Local variable table:
/*   */     //   start	length	slot	name	descriptor
/*   */     //   0	7	0	this	Lcom/aaron/authserver/model/User_Authority; } public final boolean equals(Object o) { // Byte code:
/*   */     //   0: aload_0
/*   */     //   1: aload_1
/*   */     //   2: <illegal opcode> equals : (Lcom/aaron/authserver/model/User_Authority;Ljava/lang/Object;)Z
/*   */     //   7: ireturn
/*   */     // Line number table:
/*   */     //   Java source line number -> byte code offset
/*   */     //   #5	-> 0
/*   */     // Local variable table:
/*   */     //   start	length	slot	name	descriptor
/*   */     //   0	8	0	this	Lcom/aaron/authserver/model/User_Authority;
/* 5 */     //   0	8	1	o	Ljava/lang/Object; } public String password() { return this.password; } public Boolean enabled() { return this.enabled; } public String test() { return this.test; } public List<String> authority() { return this.authority; }
/*   */    }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\model\User_Authority.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */