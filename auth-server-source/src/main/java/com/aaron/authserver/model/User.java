/*   */ package BOOT-INF.classes.com.aaron.authserver.model;public final class User extends Record { private final String username; private final String firstName; private final String lastName; private final String email; private final String password; private final String salt; private final Boolean enabled;
/*   */   
/* 3 */   public User(String username, String firstName, String lastName, String email, String password, String salt, Boolean enabled) { this.username = username; this.firstName = firstName; this.lastName = lastName; this.email = email; this.password = password; this.salt = salt; this.enabled = enabled; } public final String toString() { // Byte code:
/*   */     //   0: aload_0
/*   */     //   1: <illegal opcode> toString : (Lcom/aaron/authserver/model/User;)Ljava/lang/String;
/*   */     //   6: areturn
/*   */     // Line number table:
/*   */     //   Java source line number -> byte code offset
/*   */     //   #3	-> 0
/*   */     // Local variable table:
/*   */     //   start	length	slot	name	descriptor
/* 3 */     //   0	7	0	this	Lcom/aaron/authserver/model/User; } public String username() { return this.username; } public final int hashCode() { // Byte code:
/*   */     //   0: aload_0
/*   */     //   1: <illegal opcode> hashCode : (Lcom/aaron/authserver/model/User;)I
/*   */     //   6: ireturn
/*   */     // Line number table:
/*   */     //   Java source line number -> byte code offset
/*   */     //   #3	-> 0
/*   */     // Local variable table:
/*   */     //   start	length	slot	name	descriptor
/*   */     //   0	7	0	this	Lcom/aaron/authserver/model/User; } public final boolean equals(Object o) { // Byte code:
/*   */     //   0: aload_0
/*   */     //   1: aload_1
/*   */     //   2: <illegal opcode> equals : (Lcom/aaron/authserver/model/User;Ljava/lang/Object;)Z
/*   */     //   7: ireturn
/*   */     // Line number table:
/*   */     //   Java source line number -> byte code offset
/*   */     //   #3	-> 0
/*   */     // Local variable table:
/*   */     //   start	length	slot	name	descriptor
/*   */     //   0	8	0	this	Lcom/aaron/authserver/model/User;
/* 3 */     //   0	8	1	o	Ljava/lang/Object; } public String firstName() { return this.firstName; } public String lastName() { return this.lastName; } public String email() { return this.email; } public String password() { return this.password; } public String salt() { return this.salt; } public Boolean enabled() { return this.enabled; }
/*   */    }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\model\User.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */