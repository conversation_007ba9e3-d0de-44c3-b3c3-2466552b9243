package BOOT-INF.classes.com.aaron.authserver.model;

import com.aaron.authserver.model.AclMode;
import com.aaron.authserver.model.IDataPermission;
import java.util.Collection;

public interface ITable {
  String getTableName();
  
  AclMode getAclMode();
  
  Collection<IDataPermission> getDataPermisssions();
}


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\model\ITable.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */