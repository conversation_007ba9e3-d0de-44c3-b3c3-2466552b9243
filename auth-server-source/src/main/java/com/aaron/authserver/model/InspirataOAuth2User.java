/*    */ package BOOT-INF.classes.com.aaron.authserver.model;
/*    */ 
/*    */ import java.util.Collection;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import org.springframework.security.core.GrantedAuthority;
/*    */ import org.springframework.security.core.authority.AuthorityUtils;
/*    */ import org.springframework.security.oauth2.core.user.OAuth2User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class InspirataOAuth2User
/*    */   implements OAuth2User
/*    */ {
/* 20 */   private List<GrantedAuthority> authorities = AuthorityUtils.createAuthorityList(new String[] { "ROLE_USER" });
/*    */   
/*    */   private Map<String, Object> attributes;
/*    */   private String id;
/*    */   private String name;
/*    */   private String login;
/*    */   private String email;
/*    */   
/*    */   public Map<String, Object> getAttributes() {
/* 29 */     if (this.attributes == null) {
/* 30 */       this.attributes = new HashMap<>();
/* 31 */       this.attributes.put("id", getId());
/* 32 */       this.attributes.put("name", getName());
/* 33 */       this.attributes.put("login", getLogin());
/* 34 */       this.attributes.put("email", getEmail());
/*    */     } 
/* 36 */     return this.attributes;
/*    */   }
/*    */ 
/*    */   
/*    */   public Collection<? extends GrantedAuthority> getAuthorities() {
/* 41 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getName() {
/* 46 */     return this.name;
/*    */   }
/*    */   
/*    */   public String getId() {
/* 50 */     return this.id;
/*    */   }
/*    */   
/*    */   public void setId(String id) {
/* 54 */     this.id = id;
/*    */   }
/*    */   
/*    */   public void setName(String name) {
/* 58 */     this.name = name;
/*    */   }
/*    */   
/*    */   public String getLogin() {
/* 62 */     return this.login;
/*    */   }
/*    */   
/*    */   public void setLogin(String login) {
/* 66 */     this.login = login;
/*    */   }
/*    */   
/*    */   public String getEmail() {
/* 70 */     return this.email;
/*    */   }
/*    */   
/*    */   public void setEmail(String email) {
/* 74 */     this.email = email;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\model\InspirataOAuth2User.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */