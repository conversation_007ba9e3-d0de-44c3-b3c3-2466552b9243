/*    */ package com.aaron.authserver.model;
/*    */ 
/*    */ import java.util.Collection;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import org.springframework.security.core.GrantedAuthority;
/*    */ import org.springframework.security.core.authority.AuthorityUtils;
/*    */ import org.springframework.security.oauth2.core.user.OAuth2User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class InspirataOAuth2User
/*    */   implements OAuth2User
/*    */ {
   private List<GrantedAuthority> authorities = AuthorityUtils.createAuthorityList(new String[] { "ROLE_USER" });
/*    */   
/*    */   private Map<String, Object> attributes;
/*    */   private String id;
/*    */   private String name;
/*    */   private String login;
/*    */   private String email;
/*    */   
/*    */   public Map<String, Object> getAttributes() {
     if (this.attributes == null) {
       this.attributes = new HashMap<>();
       this.attributes.put("id", getId());
       this.attributes.put("name", getName());
       this.attributes.put("login", getLogin());
       this.attributes.put("email", getEmail());
/*    */     } 
     return this.attributes;
/*    */   }
/*    */ 
/*    */   
/*    */   public Collection<? extends GrantedAuthority> getAuthorities() {
     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getName() {
     return this.name;
/*    */   }
/*    */   
/*    */   public String getId() {
     return this.id;
/*    */   }
/*    */   
/*    */   public void setId(String id) {
     this.id = id;
/*    */   }
/*    */   
/*    */   public void setName(String name) {
     this.name = name;
/*    */   }
/*    */   
/*    */   public String getLogin() {
     return this.login;
/*    */   }
/*    */   
/*    */   public void setLogin(String login) {
     this.login = login;
/*    */   }
/*    */   
/*    */   public String getEmail() {
     return this.email;
/*    */   }
/*    */   
/*    */   public void setEmail(String email) {
     this.email = email;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\model\InspirataOAuth2User.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
