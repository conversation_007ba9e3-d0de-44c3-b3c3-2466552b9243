package com.aaron.authserver.model;

import com.aaron.authserver.model.IRole;
import com.aaron.authserver.model.ITable;
import java.util.Collection;
import java.util.Set;
import org.springframework.security.core.userdetails.UserDetails;

public interface IUser extends UserDetails {
  Object getUserId();
  
  Collection<IRole> getAuthorities();
  
  void setAuthorities(Iterable<? extends IRole> paramIterable);
  
  Set<String> getHasPermissionUrlPatterns();
  
  Collection<ITable> getPermissionTables();
  
  Object getSalt();
  
  String getEmail();
  
  String getNickName();
  
  Object getCustomDetail();
  
  boolean isActived();
  
  Object getUserInfo();
}


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\model\IUser.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
