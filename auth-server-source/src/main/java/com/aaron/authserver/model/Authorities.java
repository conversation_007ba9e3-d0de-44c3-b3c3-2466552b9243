/*   */ package BOOT-INF.classes.com.aaron.authserver.model;
/*   */ public final class Authorities extends Record { private final String authority;
/*   */   private final List<UserTest> users;
/*   */   
/* 5 */   public Authorities(String authority, List<UserTest> users) { this.authority = authority; this.users = users; } public final String toString() { // Byte code:
/*   */     //   0: aload_0
/*   */     //   1: <illegal opcode> toString : (Lcom/aaron/authserver/model/Authorities;)Ljava/lang/String;
/*   */     //   6: areturn
/*   */     // Line number table:
/*   */     //   Java source line number -> byte code offset
/*   */     //   #5	-> 0
/*   */     // Local variable table:
/*   */     //   start	length	slot	name	descriptor
/* 5 */     //   0	7	0	this	Lcom/aaron/authserver/model/Authorities; } public String authority() { return this.authority; } public final int hashCode() { // Byte code:
/*   */     //   0: aload_0
/*   */     //   1: <illegal opcode> hashCode : (Lcom/aaron/authserver/model/Authorities;)I
/*   */     //   6: ireturn
/*   */     // Line number table:
/*   */     //   Java source line number -> byte code offset
/*   */     //   #5	-> 0
/*   */     // Local variable table:
/*   */     //   start	length	slot	name	descriptor
/*   */     //   0	7	0	this	Lcom/aaron/authserver/model/Authorities; } public final boolean equals(Object o) { // Byte code:
/*   */     //   0: aload_0
/*   */     //   1: aload_1
/*   */     //   2: <illegal opcode> equals : (Lcom/aaron/authserver/model/Authorities;Ljava/lang/Object;)Z
/*   */     //   7: ireturn
/*   */     // Line number table:
/*   */     //   Java source line number -> byte code offset
/*   */     //   #5	-> 0
/*   */     // Local variable table:
/*   */     //   start	length	slot	name	descriptor
/*   */     //   0	8	0	this	Lcom/aaron/authserver/model/Authorities;
/* 5 */     //   0	8	1	o	Ljava/lang/Object; } public List<UserTest> users() { return this.users; }
/*   */    }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\model\Authorities.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */