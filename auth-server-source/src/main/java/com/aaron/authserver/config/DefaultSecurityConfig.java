package com.aaron.authserver.config;
/*    */ 
/*    */ import org.springframework.context.annotation.Configuration;
/*    */ import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Configuration
/*    */ public class DefaultSecurityConfig
/*    */ {
/*    */   public BCryptPasswordEncoder passwordEncoder() {
     return new BCryptPasswordEncoder();
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\config\DefaultSecurityConfig.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
