package BOOT-INF.classes.com.aaron.authserver.config;

import com.aaron.authserver.config.SecurityConfig$$SpringCGLIB$;
import com.aaron.authserver.service.UserService;
import com.nimbusds.jose.jwk.source.JWKSource;
import java.lang.reflect.InvocationTargetException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.cglib.core.Signature;
import org.springframework.cglib.proxy.Callback;
import org.springframework.cglib.reflect.FastClass;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;

public class null extends FastClass {
  public BeanFactory $$beanFactory;
  
  public null(Class paramClass) {
    super(paramClass);
  }
  
  public int getIndex(Signature paramSignature) {
    // Byte code:
    //   0: aload_1
    //   1: invokevirtual toString : ()Ljava/lang/String;
    //   4: dup
    //   5: invokevirtual hashCode : ()I
    //   8: lookupswitch default -> 717, -2059815923 -> 316, -1870561232 -> 327, -1779680884 -> 338, -1670183944 -> 348, -1544317662 -> 359, -1498464490 -> 370, -1457416524 -> 381, -1457386733 -> 392, -1172660267 -> 403, -1034266769 -> 414, -1025895669 -> 425, -989488491 -> 436, -942202603 -> 446, -935224611 -> 457, -879940140 -> 467, -758795190 -> 478, 92772644 -> 489, 141265702 -> 500, 246888001 -> 511, 377894268 -> 522, 444213649 -> 533, 512518181 -> 544, 527714998 -> 555, 569474166 -> 566, 652659989 -> 577, 714535052 -> 588, 903702368 -> 598, 1003204791 -> 609, 1257408990 -> 620, 1556068820 -> 631, 1621036355 -> 641, 1643801891 -> 652, 1826985398 -> 663, 1913648695 -> 674, 1948665758 -> 685, 1984935277 -> 696, 2095635076 -> 707
    //   316: ldc 'CGLIB$tokenCustomizer$6(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;'
    //   318: invokevirtual equals : (Ljava/lang/Object;)Z
    //   321: ifeq -> 718
    //   324: bipush #13
    //   326: ireturn
    //   327: ldc 'CGLIB$findMethodProxy(Lorg/springframework/cglib/core/Signature;)Lorg/springframework/cglib/proxy/MethodProxy;'
    //   329: invokevirtual equals : (Ljava/lang/Object;)Z
    //   332: ifeq -> 718
    //   335: bipush #9
    //   337: ireturn
    //   338: ldc 'defaultSecurityFilterChain(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;'
    //   340: invokevirtual equals : (Ljava/lang/Object;)Z
    //   343: ifeq -> 718
    //   346: iconst_4
    //   347: ireturn
    //   348: ldc 'clientSettings()Lorg/springframework/security/oauth2/server/authorization/settings/ClientSettings;'
    //   350: invokevirtual equals : (Ljava/lang/Object;)Z
    //   353: ifeq -> 718
    //   356: bipush #33
    //   358: ireturn
    //   359: ldc 'auth2AuthorizedClientRepository()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;'
    //   361: invokevirtual equals : (Ljava/lang/Object;)Z
    //   364: ifeq -> 718
    //   367: bipush #30
    //   369: ireturn
    //   370: ldc 'saltedDaoAuthenticationProvider()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;'
    //   372: invokevirtual equals : (Ljava/lang/Object;)Z
    //   375: ifeq -> 718
    //   378: bipush #27
    //   380: ireturn
    //   381: ldc 'CGLIB$STATICHOOK5()V'
    //   383: invokevirtual equals : (Ljava/lang/Object;)Z
    //   386: ifeq -> 718
    //   389: bipush #10
    //   391: ireturn
    //   392: ldc 'CGLIB$STATICHOOK6()V'
    //   394: invokevirtual equals : (Ljava/lang/Object;)Z
    //   397: ifeq -> 718
    //   400: bipush #16
    //   402: ireturn
    //   403: ldc 'CGLIB$accessTokenResponseClient$5()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;'
    //   405: invokevirtual equals : (Ljava/lang/Object;)Z
    //   408: ifeq -> 718
    //   411: bipush #22
    //   413: ireturn
    //   414: ldc 'CGLIB$SET_STATIC_CALLBACKS([Lorg/springframework/cglib/proxy/Callback;)V'
    //   416: invokevirtual equals : (Ljava/lang/Object;)Z
    //   419: ifeq -> 718
    //   422: bipush #17
    //   424: ireturn
    //   425: ldc 'CGLIB$SET_THREAD_CALLBACKS([Lorg/springframework/cglib/proxy/Callback;)V'
    //   427: invokevirtual equals : (Ljava/lang/Object;)Z
    //   430: ifeq -> 718
    //   433: bipush #18
    //   435: ireturn
    //   436: ldc 'jwtDecoder(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;'
    //   438: invokevirtual equals : (Ljava/lang/Object;)Z
    //   441: ifeq -> 718
    //   444: iconst_1
    //   445: ireturn
    //   446: ldc 'CGLIB$authorizationServerSettings$13()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;'
    //   448: invokevirtual equals : (Ljava/lang/Object;)Z
    //   451: ifeq -> 718
    //   454: bipush #26
    //   456: ireturn
    //   457: ldc 'webSecurityCustomizer()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;'
    //   459: invokevirtual equals : (Ljava/lang/Object;)Z
    //   462: ifeq -> 718
    //   465: iconst_5
    //   466: ireturn
    //   467: ldc 'CGLIB$authorizationRequestRepository$11()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;'
    //   469: invokevirtual equals : (Ljava/lang/Object;)Z
    //   472: ifeq -> 718
    //   475: bipush #24
    //   477: ireturn
    //   478: ldc 'CGLIB$webSecurityCustomizer$4()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;'
    //   480: invokevirtual equals : (Ljava/lang/Object;)Z
    //   483: ifeq -> 718
    //   486: bipush #21
    //   488: ireturn
    //   489: ldc 'CGLIB$saltedDaoAuthenticationProvider$9()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;'
    //   491: invokevirtual equals : (Ljava/lang/Object;)Z
    //   494: ifeq -> 718
    //   497: bipush #23
    //   499: ireturn
    //   500: ldc 'tokenSettings()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;'
    //   502: invokevirtual equals : (Ljava/lang/Object;)Z
    //   505: ifeq -> 718
    //   508: bipush #8
    //   510: ireturn
    //   511: ldc 'CGLIB$saltPasswordEncoder$2()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;'
    //   513: invokevirtual equals : (Ljava/lang/Object;)Z
    //   516: ifeq -> 718
    //   519: bipush #19
    //   521: ireturn
    //   522: ldc 'CGLIB$setBeanFactory$18(Lorg/springframework/beans/factory/BeanFactory;)V'
    //   524: invokevirtual equals : (Ljava/lang/Object;)Z
    //   527: ifeq -> 718
    //   530: bipush #15
    //   532: ireturn
    //   533: ldc 'authorizationServerSecurityFilterChain(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;'
    //   535: invokevirtual equals : (Ljava/lang/Object;)Z
    //   538: ifeq -> 718
    //   541: bipush #28
    //   543: ireturn
    //   544: ldc 'CGLIB$jwtDecoder$1(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;'
    //   546: invokevirtual equals : (Ljava/lang/Object;)Z
    //   549: ifeq -> 718
    //   552: bipush #12
    //   554: ireturn
    //   555: ldc 'CGLIB$tokenSettings$7()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;'
    //   557: invokevirtual equals : (Ljava/lang/Object;)Z
    //   560: ifeq -> 718
    //   563: bipush #14
    //   565: ireturn
    //   566: ldc 'CGLIB$auth2AuthorizedClientRepository$12()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;'
    //   568: invokevirtual equals : (Ljava/lang/Object;)Z
    //   571: ifeq -> 718
    //   574: bipush #25
    //   576: ireturn
    //   577: ldc 'authorizationRequestRepository()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;'
    //   579: invokevirtual equals : (Ljava/lang/Object;)Z
    //   582: ifeq -> 718
    //   585: bipush #29
    //   587: ireturn
    //   588: ldc 'saltPasswordEncoder()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;'
    //   590: invokevirtual equals : (Ljava/lang/Object;)Z
    //   593: ifeq -> 718
    //   596: iconst_3
    //   597: ireturn
    //   598: ldc 'authorizationServerSettings()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;'
    //   600: invokevirtual equals : (Ljava/lang/Object;)Z
    //   603: ifeq -> 718
    //   606: bipush #31
    //   608: ireturn
    //   609: ldc 'CGLIB$authorizationServerSecurityFilterChain$10(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;'
    //   611: invokevirtual equals : (Ljava/lang/Object;)Z
    //   614: ifeq -> 718
    //   617: bipush #32
    //   619: ireturn
    //   620: ldc 'CGLIB$defaultSecurityFilterChain$3(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;'
    //   622: invokevirtual equals : (Ljava/lang/Object;)Z
    //   625: ifeq -> 718
    //   628: bipush #20
    //   630: ireturn
    //   631: ldc 'saltSource()Lcom/aaron/authserver/authentication/dao/SaltSource;'
    //   633: invokevirtual equals : (Ljava/lang/Object;)Z
    //   636: ifeq -> 718
    //   639: iconst_0
    //   640: ireturn
    //   641: ldc 'accessTokenResponseClient()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;'
    //   643: invokevirtual equals : (Ljava/lang/Object;)Z
    //   646: ifeq -> 718
    //   649: bipush #6
    //   651: ireturn
    //   652: ldc 'CGLIB$saltSource$0()Lcom/aaron/authserver/authentication/dao/SaltSource;'
    //   654: invokevirtual equals : (Ljava/lang/Object;)Z
    //   657: ifeq -> 718
    //   660: bipush #11
    //   662: ireturn
    //   663: ldc 'equals(Ljava/lang/Object;)Z'
    //   665: invokevirtual equals : (Ljava/lang/Object;)Z
    //   668: ifeq -> 718
    //   671: bipush #34
    //   673: ireturn
    //   674: ldc 'toString()Ljava/lang/String;'
    //   676: invokevirtual equals : (Ljava/lang/Object;)Z
    //   679: ifeq -> 718
    //   682: bipush #35
    //   684: ireturn
    //   685: ldc 'tokenCustomizer(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;'
    //   687: invokevirtual equals : (Ljava/lang/Object;)Z
    //   690: ifeq -> 718
    //   693: bipush #7
    //   695: ireturn
    //   696: ldc 'hashCode()I'
    //   698: invokevirtual equals : (Ljava/lang/Object;)Z
    //   701: ifeq -> 718
    //   704: bipush #36
    //   706: ireturn
    //   707: ldc 'setBeanFactory(Lorg/springframework/beans/factory/BeanFactory;)V'
    //   709: invokevirtual equals : (Ljava/lang/Object;)Z
    //   712: ifeq -> 718
    //   715: iconst_2
    //   716: ireturn
    //   717: pop
    //   718: iconst_m1
    //   719: ireturn
  }
  
  public int getIndex(String paramString, Class[] paramArrayOfClass) {
    // Byte code:
    //   0: aload_1
    //   1: aload_2
    //   2: swap
    //   3: dup
    //   4: invokevirtual hashCode : ()I
    //   7: lookupswitch default -> 1811, -2053743954 -> 312, -1854716749 -> 347, -1785869299 -> 397, -1776922004 -> 445, -1774581670 -> 479, -1304897338 -> 515, -1295482945 -> 551, -1222045536 -> 601, -1195193480 -> 635, -1131754047 -> 671, -1013651080 -> 707, -724137456 -> 756, -619613427 -> 791, -479655555 -> 841, -353680448 -> 888, -124920937 -> 923, -72148052 -> 973, -60403779 -> 1007, 85179481 -> 1057, 147696667 -> 1105, 161998113 -> 1139, 161998114 -> 1175, 347787580 -> 1211, 457654623 -> 1247, 534171339 -> 1283, 705801114 -> 1318, 792467382 -> 1369, 836508259 -> 1403, 911232712 -> 1438, 1025471088 -> 1488, 1154623345 -> 1537, 1244855313 -> 1585, 1578178830 -> 1618, 1681696623 -> 1655, 1893286154 -> 1691, 1934241640 -> 1727, 2036250913 -> 1777
    //   312: ldc 'CGLIB$auth2AuthorizedClientRepository$12'
    //   314: invokevirtual equals : (Ljava/lang/Object;)Z
    //   317: ifeq -> 1812
    //   320: dup
    //   321: arraylength
    //   322: tableswitch default -> 344, 0 -> 340
    //   340: pop
    //   341: bipush #25
    //   343: ireturn
    //   344: goto -> 1815
    //   347: ldc 'CGLIB$authorizationServerSecurityFilterChain$10'
    //   349: invokevirtual equals : (Ljava/lang/Object;)Z
    //   352: ifeq -> 1812
    //   355: dup
    //   356: arraylength
    //   357: tableswitch default -> 394, 1 -> 376
    //   376: dup
    //   377: iconst_0
    //   378: aaload
    //   379: invokevirtual getName : ()Ljava/lang/String;
    //   382: ldc 'org.springframework.security.config.annotation.web.builders.HttpSecurity'
    //   384: invokevirtual equals : (Ljava/lang/Object;)Z
    //   387: ifeq -> 1815
    //   390: pop
    //   391: bipush #32
    //   393: ireturn
    //   394: goto -> 1815
    //   397: ldc 'authorizationServerSecurityFilterChain'
    //   399: invokevirtual equals : (Ljava/lang/Object;)Z
    //   402: ifeq -> 1812
    //   405: dup
    //   406: arraylength
    //   407: tableswitch default -> 442, 1 -> 424
    //   424: dup
    //   425: iconst_0
    //   426: aaload
    //   427: invokevirtual getName : ()Ljava/lang/String;
    //   430: ldc 'org.springframework.security.config.annotation.web.builders.HttpSecurity'
    //   432: invokevirtual equals : (Ljava/lang/Object;)Z
    //   435: ifeq -> 1815
    //   438: pop
    //   439: bipush #28
    //   441: ireturn
    //   442: goto -> 1815
    //   445: ldc 'toString'
    //   447: invokevirtual equals : (Ljava/lang/Object;)Z
    //   450: ifeq -> 1812
    //   453: dup
    //   454: arraylength
    //   455: tableswitch default -> 476, 0 -> 472
    //   472: pop
    //   473: bipush #35
    //   475: ireturn
    //   476: goto -> 1815
    //   479: ldc 'auth2AuthorizedClientRepository'
    //   481: invokevirtual equals : (Ljava/lang/Object;)Z
    //   484: ifeq -> 1812
    //   487: dup
    //   488: arraylength
    //   489: tableswitch default -> 512, 0 -> 508
    //   508: pop
    //   509: bipush #30
    //   511: ireturn
    //   512: goto -> 1815
    //   515: ldc 'saltedDaoAuthenticationProvider'
    //   517: invokevirtual equals : (Ljava/lang/Object;)Z
    //   520: ifeq -> 1812
    //   523: dup
    //   524: arraylength
    //   525: tableswitch default -> 548, 0 -> 544
    //   544: pop
    //   545: bipush #27
    //   547: ireturn
    //   548: goto -> 1815
    //   551: ldc 'equals'
    //   553: invokevirtual equals : (Ljava/lang/Object;)Z
    //   556: ifeq -> 1812
    //   559: dup
    //   560: arraylength
    //   561: tableswitch default -> 598, 1 -> 580
    //   580: dup
    //   581: iconst_0
    //   582: aaload
    //   583: invokevirtual getName : ()Ljava/lang/String;
    //   586: ldc 'java.lang.Object'
    //   588: invokevirtual equals : (Ljava/lang/Object;)Z
    //   591: ifeq -> 1815
    //   594: pop
    //   595: bipush #34
    //   597: ireturn
    //   598: goto -> 1815
    //   601: ldc 'CGLIB$saltSource$0'
    //   603: invokevirtual equals : (Ljava/lang/Object;)Z
    //   606: ifeq -> 1812
    //   609: dup
    //   610: arraylength
    //   611: tableswitch default -> 632, 0 -> 628
    //   628: pop
    //   629: bipush #11
    //   631: ireturn
    //   632: goto -> 1815
    //   635: ldc 'CGLIB$saltedDaoAuthenticationProvider$9'
    //   637: invokevirtual equals : (Ljava/lang/Object;)Z
    //   640: ifeq -> 1812
    //   643: dup
    //   644: arraylength
    //   645: tableswitch default -> 668, 0 -> 664
    //   664: pop
    //   665: bipush #23
    //   667: ireturn
    //   668: goto -> 1815
    //   671: ldc 'accessTokenResponseClient'
    //   673: invokevirtual equals : (Ljava/lang/Object;)Z
    //   676: ifeq -> 1812
    //   679: dup
    //   680: arraylength
    //   681: tableswitch default -> 704, 0 -> 700
    //   700: pop
    //   701: bipush #6
    //   703: ireturn
    //   704: goto -> 1815
    //   707: ldc 'setBeanFactory'
    //   709: invokevirtual equals : (Ljava/lang/Object;)Z
    //   712: ifeq -> 1812
    //   715: dup
    //   716: arraylength
    //   717: tableswitch default -> 753, 1 -> 736
    //   736: dup
    //   737: iconst_0
    //   738: aaload
    //   739: invokevirtual getName : ()Ljava/lang/String;
    //   742: ldc 'org.springframework.beans.factory.BeanFactory'
    //   744: invokevirtual equals : (Ljava/lang/Object;)Z
    //   747: ifeq -> 1815
    //   750: pop
    //   751: iconst_2
    //   752: ireturn
    //   753: goto -> 1815
    //   756: ldc 'CGLIB$webSecurityCustomizer$4'
    //   758: invokevirtual equals : (Ljava/lang/Object;)Z
    //   761: ifeq -> 1812
    //   764: dup
    //   765: arraylength
    //   766: tableswitch default -> 788, 0 -> 784
    //   784: pop
    //   785: bipush #21
    //   787: ireturn
    //   788: goto -> 1815
    //   791: ldc 'CGLIB$jwtDecoder$1'
    //   793: invokevirtual equals : (Ljava/lang/Object;)Z
    //   796: ifeq -> 1812
    //   799: dup
    //   800: arraylength
    //   801: tableswitch default -> 838, 1 -> 820
    //   820: dup
    //   821: iconst_0
    //   822: aaload
    //   823: invokevirtual getName : ()Ljava/lang/String;
    //   826: ldc 'com.nimbusds.jose.jwk.source.JWKSource'
    //   828: invokevirtual equals : (Ljava/lang/Object;)Z
    //   831: ifeq -> 1815
    //   834: pop
    //   835: bipush #12
    //   837: ireturn
    //   838: goto -> 1815
    //   841: ldc 'jwtDecoder'
    //   843: invokevirtual equals : (Ljava/lang/Object;)Z
    //   846: ifeq -> 1812
    //   849: dup
    //   850: arraylength
    //   851: tableswitch default -> 885, 1 -> 868
    //   868: dup
    //   869: iconst_0
    //   870: aaload
    //   871: invokevirtual getName : ()Ljava/lang/String;
    //   874: ldc 'com.nimbusds.jose.jwk.source.JWKSource'
    //   876: invokevirtual equals : (Ljava/lang/Object;)Z
    //   879: ifeq -> 1815
    //   882: pop
    //   883: iconst_1
    //   884: ireturn
    //   885: goto -> 1815
    //   888: ldc 'authorizationRequestRepository'
    //   890: invokevirtual equals : (Ljava/lang/Object;)Z
    //   893: ifeq -> 1812
    //   896: dup
    //   897: arraylength
    //   898: tableswitch default -> 920, 0 -> 916
    //   916: pop
    //   917: bipush #29
    //   919: ireturn
    //   920: goto -> 1815
    //   923: ldc 'CGLIB$tokenCustomizer$6'
    //   925: invokevirtual equals : (Ljava/lang/Object;)Z
    //   928: ifeq -> 1812
    //   931: dup
    //   932: arraylength
    //   933: tableswitch default -> 970, 1 -> 952
    //   952: dup
    //   953: iconst_0
    //   954: aaload
    //   955: invokevirtual getName : ()Ljava/lang/String;
    //   958: ldc 'com.aaron.authserver.service.UserService'
    //   960: invokevirtual equals : (Ljava/lang/Object;)Z
    //   963: ifeq -> 1815
    //   966: pop
    //   967: bipush #13
    //   969: ireturn
    //   970: goto -> 1815
    //   973: ldc 'CGLIB$tokenSettings$7'
    //   975: invokevirtual equals : (Ljava/lang/Object;)Z
    //   978: ifeq -> 1812
    //   981: dup
    //   982: arraylength
    //   983: tableswitch default -> 1004, 0 -> 1000
    //   1000: pop
    //   1001: bipush #14
    //   1003: ireturn
    //   1004: goto -> 1815
    //   1007: ldc 'CGLIB$SET_STATIC_CALLBACKS'
    //   1009: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1012: ifeq -> 1812
    //   1015: dup
    //   1016: arraylength
    //   1017: tableswitch default -> 1054, 1 -> 1036
    //   1036: dup
    //   1037: iconst_0
    //   1038: aaload
    //   1039: invokevirtual getName : ()Ljava/lang/String;
    //   1042: ldc '[Lorg.springframework.cglib.proxy.Callback;'
    //   1044: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1047: ifeq -> 1815
    //   1050: pop
    //   1051: bipush #17
    //   1053: ireturn
    //   1054: goto -> 1815
    //   1057: ldc 'CGLIB$SET_THREAD_CALLBACKS'
    //   1059: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1062: ifeq -> 1812
    //   1065: dup
    //   1066: arraylength
    //   1067: tableswitch default -> 1102, 1 -> 1084
    //   1084: dup
    //   1085: iconst_0
    //   1086: aaload
    //   1087: invokevirtual getName : ()Ljava/lang/String;
    //   1090: ldc '[Lorg.springframework.cglib.proxy.Callback;'
    //   1092: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1095: ifeq -> 1815
    //   1098: pop
    //   1099: bipush #18
    //   1101: ireturn
    //   1102: goto -> 1815
    //   1105: ldc 'hashCode'
    //   1107: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1110: ifeq -> 1812
    //   1113: dup
    //   1114: arraylength
    //   1115: tableswitch default -> 1136, 0 -> 1132
    //   1132: pop
    //   1133: bipush #36
    //   1135: ireturn
    //   1136: goto -> 1815
    //   1139: ldc 'CGLIB$STATICHOOK5'
    //   1141: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1144: ifeq -> 1812
    //   1147: dup
    //   1148: arraylength
    //   1149: tableswitch default -> 1172, 0 -> 1168
    //   1168: pop
    //   1169: bipush #10
    //   1171: ireturn
    //   1172: goto -> 1815
    //   1175: ldc 'CGLIB$STATICHOOK6'
    //   1177: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1180: ifeq -> 1812
    //   1183: dup
    //   1184: arraylength
    //   1185: tableswitch default -> 1208, 0 -> 1204
    //   1204: pop
    //   1205: bipush #16
    //   1207: ireturn
    //   1208: goto -> 1815
    //   1211: ldc 'tokenSettings'
    //   1213: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1216: ifeq -> 1812
    //   1219: dup
    //   1220: arraylength
    //   1221: tableswitch default -> 1244, 0 -> 1240
    //   1240: pop
    //   1241: bipush #8
    //   1243: ireturn
    //   1244: goto -> 1815
    //   1247: ldc 'authorizationServerSettings'
    //   1249: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1252: ifeq -> 1812
    //   1255: dup
    //   1256: arraylength
    //   1257: tableswitch default -> 1280, 0 -> 1276
    //   1276: pop
    //   1277: bipush #31
    //   1279: ireturn
    //   1280: goto -> 1815
    //   1283: ldc 'saltPasswordEncoder'
    //   1285: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1288: ifeq -> 1812
    //   1291: dup
    //   1292: arraylength
    //   1293: tableswitch default -> 1315, 0 -> 1312
    //   1312: pop
    //   1313: iconst_3
    //   1314: ireturn
    //   1315: goto -> 1815
    //   1318: ldc 'CGLIB$defaultSecurityFilterChain$3'
    //   1320: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1323: ifeq -> 1812
    //   1326: dup
    //   1327: arraylength
    //   1328: tableswitch default -> 1366, 1 -> 1348
    //   1348: dup
    //   1349: iconst_0
    //   1350: aaload
    //   1351: invokevirtual getName : ()Ljava/lang/String;
    //   1354: ldc 'org.springframework.security.config.annotation.web.builders.HttpSecurity'
    //   1356: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1359: ifeq -> 1815
    //   1362: pop
    //   1363: bipush #20
    //   1365: ireturn
    //   1366: goto -> 1815
    //   1369: ldc 'CGLIB$saltPasswordEncoder$2'
    //   1371: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1374: ifeq -> 1812
    //   1377: dup
    //   1378: arraylength
    //   1379: tableswitch default -> 1400, 0 -> 1396
    //   1396: pop
    //   1397: bipush #19
    //   1399: ireturn
    //   1400: goto -> 1815
    //   1403: ldc 'webSecurityCustomizer'
    //   1405: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1408: ifeq -> 1812
    //   1411: dup
    //   1412: arraylength
    //   1413: tableswitch default -> 1435, 0 -> 1432
    //   1432: pop
    //   1433: iconst_5
    //   1434: ireturn
    //   1435: goto -> 1815
    //   1438: ldc 'defaultSecurityFilterChain'
    //   1440: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1443: ifeq -> 1812
    //   1446: dup
    //   1447: arraylength
    //   1448: tableswitch default -> 1485, 1 -> 1468
    //   1468: dup
    //   1469: iconst_0
    //   1470: aaload
    //   1471: invokevirtual getName : ()Ljava/lang/String;
    //   1474: ldc 'org.springframework.security.config.annotation.web.builders.HttpSecurity'
    //   1476: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1479: ifeq -> 1815
    //   1482: pop
    //   1483: iconst_4
    //   1484: ireturn
    //   1485: goto -> 1815
    //   1488: ldc 'CGLIB$setBeanFactory$18'
    //   1490: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1493: ifeq -> 1812
    //   1496: dup
    //   1497: arraylength
    //   1498: tableswitch default -> 1534, 1 -> 1516
    //   1516: dup
    //   1517: iconst_0
    //   1518: aaload
    //   1519: invokevirtual getName : ()Ljava/lang/String;
    //   1522: ldc 'org.springframework.beans.factory.BeanFactory'
    //   1524: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1527: ifeq -> 1815
    //   1530: pop
    //   1531: bipush #15
    //   1533: ireturn
    //   1534: goto -> 1815
    //   1537: ldc 'CGLIB$findMethodProxy'
    //   1539: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1542: ifeq -> 1812
    //   1545: dup
    //   1546: arraylength
    //   1547: tableswitch default -> 1582, 1 -> 1564
    //   1564: dup
    //   1565: iconst_0
    //   1566: aaload
    //   1567: invokevirtual getName : ()Ljava/lang/String;
    //   1570: ldc 'org.springframework.cglib.core.Signature'
    //   1572: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1575: ifeq -> 1815
    //   1578: pop
    //   1579: bipush #9
    //   1581: ireturn
    //   1582: goto -> 1815
    //   1585: ldc 'saltSource'
    //   1587: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1590: ifeq -> 1812
    //   1593: dup
    //   1594: arraylength
    //   1595: tableswitch default -> 1615, 0 -> 1612
    //   1612: pop
    //   1613: iconst_0
    //   1614: ireturn
    //   1615: goto -> 1815
    //   1618: ldc 'clientSettings'
    //   1620: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1623: ifeq -> 1812
    //   1626: dup
    //   1627: arraylength
    //   1628: tableswitch default -> 1652, 0 -> 1648
    //   1648: pop
    //   1649: bipush #33
    //   1651: ireturn
    //   1652: goto -> 1815
    //   1655: ldc 'CGLIB$accessTokenResponseClient$5'
    //   1657: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1660: ifeq -> 1812
    //   1663: dup
    //   1664: arraylength
    //   1665: tableswitch default -> 1688, 0 -> 1684
    //   1684: pop
    //   1685: bipush #22
    //   1687: ireturn
    //   1688: goto -> 1815
    //   1691: ldc 'CGLIB$authorizationServerSettings$13'
    //   1693: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1696: ifeq -> 1812
    //   1699: dup
    //   1700: arraylength
    //   1701: tableswitch default -> 1724, 0 -> 1720
    //   1720: pop
    //   1721: bipush #26
    //   1723: ireturn
    //   1724: goto -> 1815
    //   1727: ldc 'tokenCustomizer'
    //   1729: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1732: ifeq -> 1812
    //   1735: dup
    //   1736: arraylength
    //   1737: tableswitch default -> 1774, 1 -> 1756
    //   1756: dup
    //   1757: iconst_0
    //   1758: aaload
    //   1759: invokevirtual getName : ()Ljava/lang/String;
    //   1762: ldc 'com.aaron.authserver.service.UserService'
    //   1764: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1767: ifeq -> 1815
    //   1770: pop
    //   1771: bipush #7
    //   1773: ireturn
    //   1774: goto -> 1815
    //   1777: ldc 'CGLIB$authorizationRequestRepository$11'
    //   1779: invokevirtual equals : (Ljava/lang/Object;)Z
    //   1782: ifeq -> 1812
    //   1785: dup
    //   1786: arraylength
    //   1787: tableswitch default -> 1808, 0 -> 1804
    //   1804: pop
    //   1805: bipush #24
    //   1807: ireturn
    //   1808: goto -> 1815
    //   1811: pop
    //   1812: goto -> 1815
    //   1815: pop
    //   1816: iconst_m1
    //   1817: ireturn
  }
  
  public int getIndex(Class[] paramArrayOfClass) {
    // Byte code:
    //   0: aload_1
    //   1: dup
    //   2: arraylength
    //   3: tableswitch default -> 23, 0 -> 20
    //   20: pop
    //   21: iconst_0
    //   22: ireturn
    //   23: goto -> 26
    //   26: pop
    //   27: iconst_m1
    //   28: ireturn
  }
  
  public Object invoke(int paramInt, Object paramObject, Object[] paramArrayOfObject) throws InvocationTargetException {
    try {
      switch (paramInt) {
        case 0:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).saltSource();
        case 1:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).jwtDecoder((JWKSource)paramArrayOfObject[0]);
        case 2:
          ((SecurityConfig$$SpringCGLIB$.null)paramObject).setBeanFactory((BeanFactory)paramArrayOfObject[0]);
          return null;
        case 3:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).saltPasswordEncoder();
        case 4:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).defaultSecurityFilterChain((HttpSecurity)paramArrayOfObject[0]);
        case 5:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).webSecurityCustomizer();
        case 6:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).accessTokenResponseClient();
        case 7:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).tokenCustomizer((UserService)paramArrayOfObject[0]);
        case 8:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).tokenSettings();
        case 9:
          return SecurityConfig$$SpringCGLIB$.null.CGLIB$findMethodProxy((Signature)paramArrayOfObject[0]);
        case 10:
          SecurityConfig$$SpringCGLIB$.null.CGLIB$STATICHOOK5();
          return null;
        case 11:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$saltSource$0();
        case 12:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$jwtDecoder$1((JWKSource)paramArrayOfObject[0]);
        case 13:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$tokenCustomizer$6((UserService)paramArrayOfObject[0]);
        case 14:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$tokenSettings$7();
        case 15:
          ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$setBeanFactory$18((BeanFactory)paramArrayOfObject[0]);
          return null;
        case 16:
          SecurityConfig$$SpringCGLIB$.null.CGLIB$STATICHOOK6();
          return null;
        case 17:
          SecurityConfig$$SpringCGLIB$.null.CGLIB$SET_STATIC_CALLBACKS((Callback[])paramArrayOfObject[0]);
          return null;
        case 18:
          SecurityConfig$$SpringCGLIB$.null.CGLIB$SET_THREAD_CALLBACKS((Callback[])paramArrayOfObject[0]);
          return null;
        case 19:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$saltPasswordEncoder$2();
        case 20:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$defaultSecurityFilterChain$3((HttpSecurity)paramArrayOfObject[0]);
        case 21:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$webSecurityCustomizer$4();
        case 22:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$accessTokenResponseClient$5();
        case 23:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$saltedDaoAuthenticationProvider$9();
        case 24:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$authorizationRequestRepository$11();
        case 25:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$auth2AuthorizedClientRepository$12();
        case 26:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$authorizationServerSettings$13();
        case 27:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).saltedDaoAuthenticationProvider();
        case 28:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).authorizationServerSecurityFilterChain((HttpSecurity)paramArrayOfObject[0]);
        case 29:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).authorizationRequestRepository();
        case 30:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).auth2AuthorizedClientRepository();
        case 31:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).authorizationServerSettings();
        case 32:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).CGLIB$authorizationServerSecurityFilterChain$10((HttpSecurity)paramArrayOfObject[0]);
        case 33:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).clientSettings();
        case 34:
          return new Boolean(((SecurityConfig$$SpringCGLIB$.null)paramObject).equals(paramArrayOfObject[0]));
        case 35:
          return ((SecurityConfig$$SpringCGLIB$.null)paramObject).toString();
        case 36:
          return new Integer(((SecurityConfig$$SpringCGLIB$.null)paramObject).hashCode());
      } 
    } catch (Throwable throwable) {
      throw new InvocationTargetException(null);
    } 
    throw new IllegalArgumentException("Cannot find matching method/constructor");
  }
  
  public Object newInstance(int paramInt, Object[] paramArrayOfObject) throws InvocationTargetException {
    try {
      switch (paramInt) {
        case 0:
          return new Object();
      } 
    } catch (Throwable throwable) {
      throw new InvocationTargetException(null);
    } 
    throw new IllegalArgumentException("Cannot find matching method/constructor");
  }
  
  public int getMaxIndex() {
    return 36;
  }
}


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\config\SecurityConfig$$SpringCGLIB$$FastClass$$1.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */