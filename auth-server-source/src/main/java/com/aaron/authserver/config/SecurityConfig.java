package com.aaron.authserver.config;

import com.aaron.authserver.authentication.dao.PasswordSaltSource;
import com.aaron.authserver.authentication.dao.SaltPasswordEncoder;
import com.aaron.authserver.authentication.dao.SaltSource;
import com.aaron.authserver.authentication.dao.SaltedDaoAuthenticationProvider;
import com.aaron.authserver.authentication.dao.SaltedSha1PasswordEncoder;
import com.aaron.authserver.service.UserService;
/*     */ import com.nimbusds.jose.jwk.source.JWKSource;
/*     */ import com.nimbusds.jose.proc.SecurityContext;
/*     */ import java.time.Duration;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import java.util.stream.Collectors;
/*     */ import org.springframework.beans.factory.annotation.Autowired;
/*     */ import org.springframework.context.annotation.Bean;
/*     */ import org.springframework.context.annotation.Configuration;
/*     */ import org.springframework.core.annotation.Order;
/*     */ import org.springframework.http.MediaType;
/*     */ import org.springframework.security.authentication.AuthenticationProvider;
/*     */ import org.springframework.security.config.Customizer;
/*     */ import org.springframework.security.config.annotation.web.builders.HttpSecurity;
/*     */ import org.springframework.security.config.annotation.web.builders.WebSecurity;
/*     */ import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
/*     */ import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;
/*     */ import org.springframework.security.config.annotation.web.configurers.CsrfConfigurer;
/*     */ import org.springframework.security.config.annotation.web.configurers.ExceptionHandlingConfigurer;
/*     */ import org.springframework.security.config.annotation.web.configurers.FormLoginConfigurer;
/*     */ import org.springframework.security.config.annotation.web.configurers.oauth2.server.resource.OAuth2ResourceServerConfigurer;
/*     */ import org.springframework.security.core.Authentication;
/*     */ import org.springframework.security.core.GrantedAuthority;
/*     */ import org.springframework.security.core.userdetails.UserDetailsService;
/*     */ import org.springframework.security.crypto.password.PasswordEncoder;
/*     */ import org.springframework.security.oauth2.client.endpoint.DefaultAuthorizationCodeTokenResponseClient;
/*     */ import org.springframework.security.oauth2.client.endpoint.OAuth2AccessTokenResponseClient;
/*     */ import org.springframework.security.oauth2.client.endpoint.OAuth2AuthorizationCodeGrantRequest;
/*     */ import org.springframework.security.oauth2.client.web.AuthorizationRequestRepository;
/*     */ import org.springframework.security.oauth2.client.web.HttpSessionOAuth2AuthorizationRequestRepository;
/*     */ import org.springframework.security.oauth2.client.web.HttpSessionOAuth2AuthorizedClientRepository;
/*     */ import org.springframework.security.oauth2.core.endpoint.OAuth2AuthorizationRequest;
/*     */ import org.springframework.security.oauth2.core.oidc.OidcUserInfo;
/*     */ import org.springframework.security.oauth2.jwt.JwtDecoder;
/*     */ import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
/*     */ import org.springframework.security.oauth2.server.authorization.config.annotation.web.configuration.OAuth2AuthorizationServerConfiguration;
/*     */ import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationEndpointConfigurer;
/*     */ import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationServerConfigurer;
/*     */ import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
/*     */ import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
/*     */ import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
/*     */ import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
/*     */ import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;
/*     */ import org.springframework.security.web.AuthenticationEntryPoint;
/*     */ import org.springframework.security.web.SecurityFilterChain;
/*     */ import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;
/*     */ import org.springframework.security.web.util.matcher.MediaTypeRequestMatcher;
/*     */ import org.springframework.security.web.util.matcher.RequestMatcher;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Configuration
/*     */ public class SecurityConfig
/*     */ {
/*     */   @Autowired
/*     */   UserDetailsService userDetailsService;
/*     */   
/*     */   @Bean
/*     */   @Order(1)
/*     */   SecurityFilterChain authorizationServerSecurityFilterChain(HttpSecurity http) throws Exception {
/*  87 */     OAuth2AuthorizationServerConfiguration.applyDefaultSecurity(http);
/*  88 */     ((OAuth2AuthorizationServerConfigurer)http.getConfigurer(OAuth2AuthorizationServerConfigurer.class))
/*     */       
/*  90 */       .oidc(Customizer.withDefaults());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  99 */     http
/* 100 */       .exceptionHandling(exceptions -> exceptions.defaultAuthenticationEntryPointFor((AuthenticationEntryPoint)new LoginUrlAuthenticationEntryPoint("/login"), (RequestMatcher)new MediaTypeRequestMatcher(new MediaType[] {
/*     */               
/*     */               MediaType.TEXT_HTML
/*     */ 
/*     */ 
/*     */             
/* 106 */             }))).csrf(csrf -> csrf.ignoringRequestMatchers(new RequestMatcher[] {
/*     */             
/*     */             ((OAuth2AuthorizationServerConfigurer)http.getConfigurer(OAuth2AuthorizationServerConfigurer.class)).getEndpointsMatcher()
/* 109 */           })).oauth2ResourceServer(resourceServer -> resourceServer.jwt(Customizer.withDefaults()));
/*     */ 
/*     */     
/* 112 */     http.authenticationProvider((AuthenticationProvider)saltedDaoAuthenticationProvider());
/* 113 */     ((OAuth2AuthorizationServerConfigurer)http.getConfigurer(OAuth2AuthorizationServerConfigurer.class)).authorizationEndpoint(auth -> auth.authenticationProvider((AuthenticationProvider)saltedDaoAuthenticationProvider()));
/*     */ 
/*     */     
/* 116 */     return (SecurityFilterChain)http.build();
/*     */   }
/*     */ 
/*     */   
/*     */   @Bean
/*     */   @Order(2)
/*     */   SecurityFilterChain defaultSecurityFilterChain(HttpSecurity http) throws Exception {
/* 123 */     http
/* 124 */       .authorizeHttpRequests(authorize -> ((AuthorizeHttpRequestsConfigurer.AuthorizedUrl)((AuthorizeHttpRequestsConfigurer.AuthorizedUrl)authorize.requestMatchers(new String[] {
/*     */             
/*     */             "/error"
/* 127 */           })).permitAll().anyRequest()).authenticated()).formLogin(formLogin -> formLogin.loginPage("/login").permitAll());
/*     */ 
/*     */ 
/*     */     
/* 131 */     return (SecurityFilterChain)http.build();
/*     */   }
/*     */   
/*     */   @Bean
/*     */   WebSecurityCustomizer webSecurityCustomizer() {
/* 136 */     return web -> web.debug(false).ignoring().requestMatchers(new String[] { "/webjars/**", "/images/**", "/css/**", "/assets/**", "/favicon.ico" });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Bean
/*     */   public AuthorizationRequestRepository<OAuth2AuthorizationRequest> authorizationRequestRepository() {
/* 151 */     return (AuthorizationRequestRepository<OAuth2AuthorizationRequest>)new HttpSessionOAuth2AuthorizationRequestRepository();
/*     */   }
/*     */   
/*     */   @Bean
/*     */   public HttpSessionOAuth2AuthorizedClientRepository auth2AuthorizedClientRepository() {
/* 156 */     return new HttpSessionOAuth2AuthorizedClientRepository();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Bean
/*     */   public OAuth2AccessTokenResponseClient<OAuth2AuthorizationCodeGrantRequest> accessTokenResponseClient() {
/* 186 */     DefaultAuthorizationCodeTokenResponseClient accessTokenResponseClient = new DefaultAuthorizationCodeTokenResponseClient();
/* 187 */     return (OAuth2AccessTokenResponseClient<OAuth2AuthorizationCodeGrantRequest>)accessTokenResponseClient;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Bean({"saltedDaoAuthenticationProvider"})
/*     */   public SaltedDaoAuthenticationProvider saltedDaoAuthenticationProvider() {
/* 202 */     SaltedDaoAuthenticationProvider authenticationProvider = new SaltedDaoAuthenticationProvider();
/* 203 */     authenticationProvider.setUserDetailsService(this.userDetailsService);
/* 204 */     authenticationProvider.setPasswordEncoder((PasswordEncoder)saltPasswordEncoder());
/* 205 */     authenticationProvider.setSaltSource(saltSource());
/*     */     
/* 207 */     return authenticationProvider;
/*     */   }
/*     */   
/*     */   @Bean
/*     */   public SaltSource saltSource() {
/* 212 */     return (SaltSource)new PasswordSaltSource();
/*     */   }
/*     */   
/*     */   @Bean
/*     */   public SaltPasswordEncoder saltPasswordEncoder() {
/* 217 */     return (SaltPasswordEncoder)new SaltedSha1PasswordEncoder();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Bean
/*     */   OAuth2TokenCustomizer<JwtEncodingContext> tokenCustomizer(UserService userService) {
/* 239 */     return context -> {
/*     */         Authentication principal = context.getPrincipal();
/*     */         String username = principal.getName();
/*     */         if (OAuth2TokenType.ACCESS_TOKEN.equals(context.getTokenType())) {
/*     */           Set<String> authorities = (Set<String>)principal.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toSet());
/*     */           context.getClaims().claim("authorities", authorities);
/*     */         } 
/*     */         if ("id_token".equals(context.getTokenType().getValue())) {
/*     */           OidcUserInfo userInfo = userService.getOidcUserInfoByEmail(context.getPrincipal().getName());
/*     */           context.getClaims().claims(claims -> claims.putAll(userInfo.getClaims()));
/*     */         } 
/*     */       };
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Bean
/*     */   public JwtDecoder jwtDecoder(JWKSource<SecurityContext> jwkSource) {
/* 294 */     return OAuth2AuthorizationServerConfiguration.jwtDecoder(jwkSource);
/*     */   }
/*     */ 
/*     */   
/*     */   @Bean
/*     */   public TokenSettings tokenSettings() {
/* 300 */     return TokenSettings.builder().accessTokenTimeToLive(Duration.ofMinutes(30L)).build();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Bean
/*     */   public AuthorizationServerSettings authorizationServerSettings() {
/* 354 */     return AuthorizationServerSettings.builder().build();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   ClientSettings clientSettings() {
/* 361 */     return ClientSettings.builder()
/* 362 */       .requireAuthorizationConsent(true)
/* 363 */       .requireProofKey(true)
/* 364 */       .build();
/*     */   }
/*     */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\config\SecurityConfig.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */