package BOOT-INF.classes.com.aaron.authserver.config;

import com.aaron.authserver.config.SecurityConfig;
import com.aaron.authserver.service.UserService;
import com.nimbusds.jose.jwk.source.JWKSource;
import java.lang.reflect.InvocationTargetException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.cglib.core.Signature;
import org.springframework.cglib.reflect.FastClass;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;

public class null extends FastClass {
  public BeanFactory $$beanFactory;
  
  public null(Class paramClass) {
    super(paramClass);
  }
  
  public int getIndex(Signature paramSignature) {
    // Byte code:
    //   0: aload_1
    //   1: invokevirtual toString : ()Ljava/lang/String;
    //   4: dup
    //   5: invokevirtual hashCode : ()I
    //   8: lookupswitch default -> 337, -1779680884 -> 156, -1670183944 -> 166, -1544317662 -> 177, -1498464490 -> 188, -989488491 -> 199, -935224611 -> 209, 141265702 -> 219, 444213649 -> 230, 652659989 -> 241, 714535052 -> 252, 903702368 -> 262, 1556068820 -> 273, 1621036355 -> 283, 1826985398 -> 293, 1913648695 -> 304, 1948665758 -> 315, 1984935277 -> 326
    //   156: ldc 'defaultSecurityFilterChain(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;'
    //   158: invokevirtual equals : (Ljava/lang/Object;)Z
    //   161: ifeq -> 338
    //   164: iconst_3
    //   165: ireturn
    //   166: ldc 'clientSettings()Lorg/springframework/security/oauth2/server/authorization/settings/ClientSettings;'
    //   168: invokevirtual equals : (Ljava/lang/Object;)Z
    //   171: ifeq -> 338
    //   174: bipush #8
    //   176: ireturn
    //   177: ldc 'auth2AuthorizedClientRepository()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;'
    //   179: invokevirtual equals : (Ljava/lang/Object;)Z
    //   182: ifeq -> 338
    //   185: bipush #12
    //   187: ireturn
    //   188: ldc 'saltedDaoAuthenticationProvider()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;'
    //   190: invokevirtual equals : (Ljava/lang/Object;)Z
    //   193: ifeq -> 338
    //   196: bipush #9
    //   198: ireturn
    //   199: ldc 'jwtDecoder(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;'
    //   201: invokevirtual equals : (Ljava/lang/Object;)Z
    //   204: ifeq -> 338
    //   207: iconst_1
    //   208: ireturn
    //   209: ldc 'webSecurityCustomizer()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;'
    //   211: invokevirtual equals : (Ljava/lang/Object;)Z
    //   214: ifeq -> 338
    //   217: iconst_4
    //   218: ireturn
    //   219: ldc 'tokenSettings()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;'
    //   221: invokevirtual equals : (Ljava/lang/Object;)Z
    //   224: ifeq -> 338
    //   227: bipush #7
    //   229: ireturn
    //   230: ldc 'authorizationServerSecurityFilterChain(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;'
    //   232: invokevirtual equals : (Ljava/lang/Object;)Z
    //   235: ifeq -> 338
    //   238: bipush #10
    //   240: ireturn
    //   241: ldc 'authorizationRequestRepository()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;'
    //   243: invokevirtual equals : (Ljava/lang/Object;)Z
    //   246: ifeq -> 338
    //   249: bipush #11
    //   251: ireturn
    //   252: ldc 'saltPasswordEncoder()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;'
    //   254: invokevirtual equals : (Ljava/lang/Object;)Z
    //   257: ifeq -> 338
    //   260: iconst_2
    //   261: ireturn
    //   262: ldc 'authorizationServerSettings()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;'
    //   264: invokevirtual equals : (Ljava/lang/Object;)Z
    //   267: ifeq -> 338
    //   270: bipush #13
    //   272: ireturn
    //   273: ldc 'saltSource()Lcom/aaron/authserver/authentication/dao/SaltSource;'
    //   275: invokevirtual equals : (Ljava/lang/Object;)Z
    //   278: ifeq -> 338
    //   281: iconst_0
    //   282: ireturn
    //   283: ldc 'accessTokenResponseClient()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;'
    //   285: invokevirtual equals : (Ljava/lang/Object;)Z
    //   288: ifeq -> 338
    //   291: iconst_5
    //   292: ireturn
    //   293: ldc 'equals(Ljava/lang/Object;)Z'
    //   295: invokevirtual equals : (Ljava/lang/Object;)Z
    //   298: ifeq -> 338
    //   301: bipush #14
    //   303: ireturn
    //   304: ldc 'toString()Ljava/lang/String;'
    //   306: invokevirtual equals : (Ljava/lang/Object;)Z
    //   309: ifeq -> 338
    //   312: bipush #15
    //   314: ireturn
    //   315: ldc 'tokenCustomizer(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;'
    //   317: invokevirtual equals : (Ljava/lang/Object;)Z
    //   320: ifeq -> 338
    //   323: bipush #6
    //   325: ireturn
    //   326: ldc 'hashCode()I'
    //   328: invokevirtual equals : (Ljava/lang/Object;)Z
    //   331: ifeq -> 338
    //   334: bipush #16
    //   336: ireturn
    //   337: pop
    //   338: iconst_m1
    //   339: ireturn
  }
  
  public int getIndex(String paramString, Class[] paramArrayOfClass) {
    // Byte code:
    //   0: aload_1
    //   1: aload_2
    //   2: swap
    //   3: dup
    //   4: invokevirtual hashCode : ()I
    //   7: lookupswitch default -> 825, -1785869299 -> 152, -1776922004 -> 201, -1774581670 -> 235, -1304897338 -> 271, -1295482945 -> 307, -1131754047 -> 357, -479655555 -> 390, -353680448 -> 440, 147696667 -> 475, 347787580 -> 511, 457654623 -> 547, 534171339 -> 583, 836508259 -> 618, 911232712 -> 654, 1244855313 -> 704, 1578178830 -> 738, 1934241640 -> 775
    //   152: ldc 'authorizationServerSecurityFilterChain'
    //   154: invokevirtual equals : (Ljava/lang/Object;)Z
    //   157: ifeq -> 826
    //   160: dup
    //   161: arraylength
    //   162: tableswitch default -> 198, 1 -> 180
    //   180: dup
    //   181: iconst_0
    //   182: aaload
    //   183: invokevirtual getName : ()Ljava/lang/String;
    //   186: ldc 'org.springframework.security.config.annotation.web.builders.HttpSecurity'
    //   188: invokevirtual equals : (Ljava/lang/Object;)Z
    //   191: ifeq -> 829
    //   194: pop
    //   195: bipush #10
    //   197: ireturn
    //   198: goto -> 829
    //   201: ldc 'toString'
    //   203: invokevirtual equals : (Ljava/lang/Object;)Z
    //   206: ifeq -> 826
    //   209: dup
    //   210: arraylength
    //   211: tableswitch default -> 232, 0 -> 228
    //   228: pop
    //   229: bipush #15
    //   231: ireturn
    //   232: goto -> 829
    //   235: ldc 'auth2AuthorizedClientRepository'
    //   237: invokevirtual equals : (Ljava/lang/Object;)Z
    //   240: ifeq -> 826
    //   243: dup
    //   244: arraylength
    //   245: tableswitch default -> 268, 0 -> 264
    //   264: pop
    //   265: bipush #12
    //   267: ireturn
    //   268: goto -> 829
    //   271: ldc 'saltedDaoAuthenticationProvider'
    //   273: invokevirtual equals : (Ljava/lang/Object;)Z
    //   276: ifeq -> 826
    //   279: dup
    //   280: arraylength
    //   281: tableswitch default -> 304, 0 -> 300
    //   300: pop
    //   301: bipush #9
    //   303: ireturn
    //   304: goto -> 829
    //   307: ldc 'equals'
    //   309: invokevirtual equals : (Ljava/lang/Object;)Z
    //   312: ifeq -> 826
    //   315: dup
    //   316: arraylength
    //   317: tableswitch default -> 354, 1 -> 336
    //   336: dup
    //   337: iconst_0
    //   338: aaload
    //   339: invokevirtual getName : ()Ljava/lang/String;
    //   342: ldc 'java.lang.Object'
    //   344: invokevirtual equals : (Ljava/lang/Object;)Z
    //   347: ifeq -> 829
    //   350: pop
    //   351: bipush #14
    //   353: ireturn
    //   354: goto -> 829
    //   357: ldc 'accessTokenResponseClient'
    //   359: invokevirtual equals : (Ljava/lang/Object;)Z
    //   362: ifeq -> 826
    //   365: dup
    //   366: arraylength
    //   367: tableswitch default -> 387, 0 -> 384
    //   384: pop
    //   385: iconst_5
    //   386: ireturn
    //   387: goto -> 829
    //   390: ldc 'jwtDecoder'
    //   392: invokevirtual equals : (Ljava/lang/Object;)Z
    //   395: ifeq -> 826
    //   398: dup
    //   399: arraylength
    //   400: tableswitch default -> 437, 1 -> 420
    //   420: dup
    //   421: iconst_0
    //   422: aaload
    //   423: invokevirtual getName : ()Ljava/lang/String;
    //   426: ldc 'com.nimbusds.jose.jwk.source.JWKSource'
    //   428: invokevirtual equals : (Ljava/lang/Object;)Z
    //   431: ifeq -> 829
    //   434: pop
    //   435: iconst_1
    //   436: ireturn
    //   437: goto -> 829
    //   440: ldc 'authorizationRequestRepository'
    //   442: invokevirtual equals : (Ljava/lang/Object;)Z
    //   445: ifeq -> 826
    //   448: dup
    //   449: arraylength
    //   450: tableswitch default -> 472, 0 -> 468
    //   468: pop
    //   469: bipush #11
    //   471: ireturn
    //   472: goto -> 829
    //   475: ldc 'hashCode'
    //   477: invokevirtual equals : (Ljava/lang/Object;)Z
    //   480: ifeq -> 826
    //   483: dup
    //   484: arraylength
    //   485: tableswitch default -> 508, 0 -> 504
    //   504: pop
    //   505: bipush #16
    //   507: ireturn
    //   508: goto -> 829
    //   511: ldc 'tokenSettings'
    //   513: invokevirtual equals : (Ljava/lang/Object;)Z
    //   516: ifeq -> 826
    //   519: dup
    //   520: arraylength
    //   521: tableswitch default -> 544, 0 -> 540
    //   540: pop
    //   541: bipush #7
    //   543: ireturn
    //   544: goto -> 829
    //   547: ldc 'authorizationServerSettings'
    //   549: invokevirtual equals : (Ljava/lang/Object;)Z
    //   552: ifeq -> 826
    //   555: dup
    //   556: arraylength
    //   557: tableswitch default -> 580, 0 -> 576
    //   576: pop
    //   577: bipush #13
    //   579: ireturn
    //   580: goto -> 829
    //   583: ldc 'saltPasswordEncoder'
    //   585: invokevirtual equals : (Ljava/lang/Object;)Z
    //   588: ifeq -> 826
    //   591: dup
    //   592: arraylength
    //   593: tableswitch default -> 615, 0 -> 612
    //   612: pop
    //   613: iconst_2
    //   614: ireturn
    //   615: goto -> 829
    //   618: ldc 'webSecurityCustomizer'
    //   620: invokevirtual equals : (Ljava/lang/Object;)Z
    //   623: ifeq -> 826
    //   626: dup
    //   627: arraylength
    //   628: tableswitch default -> 651, 0 -> 648
    //   648: pop
    //   649: iconst_4
    //   650: ireturn
    //   651: goto -> 829
    //   654: ldc 'defaultSecurityFilterChain'
    //   656: invokevirtual equals : (Ljava/lang/Object;)Z
    //   659: ifeq -> 826
    //   662: dup
    //   663: arraylength
    //   664: tableswitch default -> 701, 1 -> 684
    //   684: dup
    //   685: iconst_0
    //   686: aaload
    //   687: invokevirtual getName : ()Ljava/lang/String;
    //   690: ldc 'org.springframework.security.config.annotation.web.builders.HttpSecurity'
    //   692: invokevirtual equals : (Ljava/lang/Object;)Z
    //   695: ifeq -> 829
    //   698: pop
    //   699: iconst_3
    //   700: ireturn
    //   701: goto -> 829
    //   704: ldc 'saltSource'
    //   706: invokevirtual equals : (Ljava/lang/Object;)Z
    //   709: ifeq -> 826
    //   712: dup
    //   713: arraylength
    //   714: tableswitch default -> 735, 0 -> 732
    //   732: pop
    //   733: iconst_0
    //   734: ireturn
    //   735: goto -> 829
    //   738: ldc 'clientSettings'
    //   740: invokevirtual equals : (Ljava/lang/Object;)Z
    //   743: ifeq -> 826
    //   746: dup
    //   747: arraylength
    //   748: tableswitch default -> 772, 0 -> 768
    //   768: pop
    //   769: bipush #8
    //   771: ireturn
    //   772: goto -> 829
    //   775: ldc 'tokenCustomizer'
    //   777: invokevirtual equals : (Ljava/lang/Object;)Z
    //   780: ifeq -> 826
    //   783: dup
    //   784: arraylength
    //   785: tableswitch default -> 822, 1 -> 804
    //   804: dup
    //   805: iconst_0
    //   806: aaload
    //   807: invokevirtual getName : ()Ljava/lang/String;
    //   810: ldc 'com.aaron.authserver.service.UserService'
    //   812: invokevirtual equals : (Ljava/lang/Object;)Z
    //   815: ifeq -> 829
    //   818: pop
    //   819: bipush #6
    //   821: ireturn
    //   822: goto -> 829
    //   825: pop
    //   826: goto -> 829
    //   829: pop
    //   830: iconst_m1
    //   831: ireturn
  }
  
  public int getIndex(Class[] paramArrayOfClass) {
    // Byte code:
    //   0: aload_1
    //   1: dup
    //   2: arraylength
    //   3: tableswitch default -> 23, 0 -> 20
    //   20: pop
    //   21: iconst_0
    //   22: ireturn
    //   23: goto -> 26
    //   26: pop
    //   27: iconst_m1
    //   28: ireturn
  }
  
  public Object invoke(int paramInt, Object paramObject, Object[] paramArrayOfObject) throws InvocationTargetException {
    try {
      switch (paramInt) {
        case 0:
          return ((SecurityConfig)paramObject).saltSource();
        case 1:
          return ((SecurityConfig)paramObject).jwtDecoder((JWKSource)paramArrayOfObject[0]);
        case 2:
          return ((SecurityConfig)paramObject).saltPasswordEncoder();
        case 3:
          return ((SecurityConfig)paramObject).defaultSecurityFilterChain((HttpSecurity)paramArrayOfObject[0]);
        case 4:
          return ((SecurityConfig)paramObject).webSecurityCustomizer();
        case 5:
          return ((SecurityConfig)paramObject).accessTokenResponseClient();
        case 6:
          return ((SecurityConfig)paramObject).tokenCustomizer((UserService)paramArrayOfObject[0]);
        case 7:
          return ((SecurityConfig)paramObject).tokenSettings();
        case 8:
          return ((SecurityConfig)paramObject).clientSettings();
        case 9:
          return ((SecurityConfig)paramObject).saltedDaoAuthenticationProvider();
        case 10:
          return ((SecurityConfig)paramObject).authorizationServerSecurityFilterChain((HttpSecurity)paramArrayOfObject[0]);
        case 11:
          return ((SecurityConfig)paramObject).authorizationRequestRepository();
        case 12:
          return ((SecurityConfig)paramObject).auth2AuthorizedClientRepository();
        case 13:
          return ((SecurityConfig)paramObject).authorizationServerSettings();
        case 14:
          return new Boolean(((SecurityConfig)paramObject).equals(paramArrayOfObject[0]));
        case 15:
          return ((SecurityConfig)paramObject).toString();
        case 16:
          return new Integer(((SecurityConfig)paramObject).hashCode());
      } 
    } catch (Throwable throwable) {
      throw new InvocationTargetException(null);
    } 
    throw new IllegalArgumentException("Cannot find matching method/constructor");
  }
  
  public Object newInstance(int paramInt, Object[] paramArrayOfObject) throws InvocationTargetException {
    try {
      switch (paramInt) {
        case 0:
          return new SecurityConfig();
      } 
    } catch (Throwable throwable) {
      throw new InvocationTargetException(null);
    } 
    throw new IllegalArgumentException("Cannot find matching method/constructor");
  }
  
  public int getMaxIndex() {
    return 16;
  }
}


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\config\SecurityConfig$$SpringCGLIB$$FastClass$$0.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */