/*     */ package BOOT-INF.classes.com.aaron.authserver.config;
/*     */ 
/*     */ import com.aaron.authserver.authentication.dao.SaltPasswordEncoder;
/*     */ import com.aaron.authserver.authentication.dao.SaltSource;
/*     */ import com.aaron.authserver.authentication.dao.SaltedDaoAuthenticationProvider;
/*     */ import com.aaron.authserver.config.SecurityConfig;
/*     */ import com.aaron.authserver.config.SecurityConfig__Autowiring;
/*     */ import com.aaron.authserver.service.UserService;
/*     */ import com.nimbusds.jose.jwk.source.JWKSource;
/*     */ import java.util.function.Supplier;
/*     */ import org.springframework.beans.factory.aot.AutowiredArguments;
/*     */ import org.springframework.beans.factory.aot.BeanInstanceSupplier;
/*     */ import org.springframework.beans.factory.config.BeanDefinition;
/*     */ import org.springframework.beans.factory.support.InstanceSupplier;
/*     */ import org.springframework.beans.factory.support.RegisteredBean;
/*     */ import org.springframework.beans.factory.support.RootBeanDefinition;
/*     */ import org.springframework.context.annotation.ConfigurationClassUtils;
/*     */ import org.springframework.core.ResolvableType;
/*     */ import org.springframework.security.config.annotation.web.builders.HttpSecurity;
/*     */ import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
/*     */ import org.springframework.security.oauth2.client.endpoint.OAuth2AccessTokenResponseClient;
/*     */ import org.springframework.security.oauth2.client.endpoint.OAuth2AuthorizationCodeGrantRequest;
/*     */ import org.springframework.security.oauth2.client.web.AuthorizationRequestRepository;
/*     */ import org.springframework.security.oauth2.client.web.HttpSessionOAuth2AuthorizedClientRepository;
/*     */ import org.springframework.security.oauth2.core.endpoint.OAuth2AuthorizationRequest;
/*     */ import org.springframework.security.oauth2.jwt.JwtDecoder;
/*     */ import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
/*     */ import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
/*     */ import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
/*     */ import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;
/*     */ import org.springframework.security.web.SecurityFilterChain;
/*     */ import org.springframework.util.function.ThrowingSupplier;
/*     */ 
/*     */ public class SecurityConfig__BeanDefinitions {
/*     */   public static BeanDefinition getSecurityConfigBeanDefinition() {
/*  36 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(SecurityConfig.class);
/*  37 */     rootBeanDefinition.setTargetType(SecurityConfig.class);
/*  38 */     ConfigurationClassUtils.initializeConfigurationClass(SecurityConfig.class);
/*  39 */     InstanceSupplier instanceSupplier = InstanceSupplier.using(null::new);
/*  40 */     instanceSupplier = instanceSupplier.andThen(SecurityConfig__Autowiring::apply);
/*  41 */     rootBeanDefinition.setInstanceSupplier((Supplier)instanceSupplier);
/*  42 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<SecurityFilterChain> getAuthorizationServerSecurityFilterChainInstanceSupplier() {
/*  50 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "authorizationServerSecurityFilterChain", new Class[] { HttpSecurity.class
/*  51 */         }).withGenerator((paramRegisteredBean, paramAutowiredArguments) -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).authorizationServerSecurityFilterChain((HttpSecurity)paramAutowiredArguments.get(0)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getAuthorizationServerSecurityFilterChainBeanDefinition() {
/*  58 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(SecurityFilterChain.class);
/*  59 */     rootBeanDefinition.setInstanceSupplier((Supplier)getAuthorizationServerSecurityFilterChainInstanceSupplier());
/*  60 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<SecurityFilterChain> getDefaultSecurityFilterChainInstanceSupplier() {
/*  68 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "defaultSecurityFilterChain", new Class[] { HttpSecurity.class
/*  69 */         }).withGenerator((paramRegisteredBean, paramAutowiredArguments) -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).defaultSecurityFilterChain((HttpSecurity)paramAutowiredArguments.get(0)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getDefaultSecurityFilterChainBeanDefinition() {
/*  76 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(SecurityFilterChain.class);
/*  77 */     rootBeanDefinition.setInstanceSupplier((Supplier)getDefaultSecurityFilterChainInstanceSupplier());
/*  78 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<WebSecurityCustomizer> getWebSecurityCustomizerInstanceSupplier() {
/*  86 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "webSecurityCustomizer", new Class[0])
/*  87 */       .withGenerator(paramRegisteredBean -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).webSecurityCustomizer());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getWebSecurityCustomizerBeanDefinition() {
/*  94 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(WebSecurityCustomizer.class);
/*  95 */     rootBeanDefinition.setInstanceSupplier((Supplier)getWebSecurityCustomizerInstanceSupplier());
/*  96 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<AuthorizationRequestRepository> getAuthorizationRequestRepositoryInstanceSupplier() {
/* 104 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "authorizationRequestRepository", new Class[0])
/* 105 */       .withGenerator(paramRegisteredBean -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).authorizationRequestRepository());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getAuthorizationRequestRepositoryBeanDefinition() {
/* 112 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(AuthorizationRequestRepository.class);
/* 113 */     rootBeanDefinition.setTargetType(ResolvableType.forClassWithGenerics(AuthorizationRequestRepository.class, new Class[] { OAuth2AuthorizationRequest.class }));
/* 114 */     rootBeanDefinition.setInstanceSupplier((Supplier)getAuthorizationRequestRepositoryInstanceSupplier());
/* 115 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<HttpSessionOAuth2AuthorizedClientRepository> getAuthAuthorizedClientRepositoryInstanceSupplier() {
/* 123 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "auth2AuthorizedClientRepository", new Class[0])
/* 124 */       .withGenerator(paramRegisteredBean -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).auth2AuthorizedClientRepository());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getAuthAuthorizedClientRepositoryBeanDefinition() {
/* 131 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(HttpSessionOAuth2AuthorizedClientRepository.class);
/* 132 */     rootBeanDefinition.setInstanceSupplier((Supplier)getAuthAuthorizedClientRepositoryInstanceSupplier());
/* 133 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<OAuth2AccessTokenResponseClient> getAccessTokenResponseClientInstanceSupplier() {
/* 141 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "accessTokenResponseClient", new Class[0])
/* 142 */       .withGenerator(paramRegisteredBean -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).accessTokenResponseClient());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getAccessTokenResponseClientBeanDefinition() {
/* 149 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(OAuth2AccessTokenResponseClient.class);
/* 150 */     rootBeanDefinition.setTargetType(ResolvableType.forClassWithGenerics(OAuth2AccessTokenResponseClient.class, new Class[] { OAuth2AuthorizationCodeGrantRequest.class }));
/* 151 */     rootBeanDefinition.setInstanceSupplier((Supplier)getAccessTokenResponseClientInstanceSupplier());
/* 152 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<SaltedDaoAuthenticationProvider> getSaltedDaoAuthenticationProviderInstanceSupplier() {
/* 160 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "saltedDaoAuthenticationProvider", new Class[0])
/* 161 */       .withGenerator(paramRegisteredBean -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).saltedDaoAuthenticationProvider());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getSaltedDaoAuthenticationProviderBeanDefinition() {
/* 168 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(SaltedDaoAuthenticationProvider.class);
/* 169 */     rootBeanDefinition.setInstanceSupplier((Supplier)getSaltedDaoAuthenticationProviderInstanceSupplier());
/* 170 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<SaltSource> getSaltSourceInstanceSupplier() {
/* 177 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "saltSource", new Class[0])
/* 178 */       .withGenerator(paramRegisteredBean -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).saltSource());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getSaltSourceBeanDefinition() {
/* 185 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(SaltSource.class);
/* 186 */     rootBeanDefinition.setInstanceSupplier((Supplier)getSaltSourceInstanceSupplier());
/* 187 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<SaltPasswordEncoder> getSaltPasswordEncoderInstanceSupplier() {
/* 195 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "saltPasswordEncoder", new Class[0])
/* 196 */       .withGenerator(paramRegisteredBean -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).saltPasswordEncoder());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getSaltPasswordEncoderBeanDefinition() {
/* 203 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(SaltPasswordEncoder.class);
/* 204 */     rootBeanDefinition.setInstanceSupplier((Supplier)getSaltPasswordEncoderInstanceSupplier());
/* 205 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<OAuth2TokenCustomizer> getTokenCustomizerInstanceSupplier() {
/* 212 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "tokenCustomizer", new Class[] { UserService.class
/* 213 */         }).withGenerator((paramRegisteredBean, paramAutowiredArguments) -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).tokenCustomizer((UserService)paramAutowiredArguments.get(0)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getTokenCustomizerBeanDefinition() {
/* 220 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(OAuth2TokenCustomizer.class);
/* 221 */     rootBeanDefinition.setTargetType(ResolvableType.forClassWithGenerics(OAuth2TokenCustomizer.class, new Class[] { JwtEncodingContext.class }));
/* 222 */     rootBeanDefinition.setInstanceSupplier((Supplier)getTokenCustomizerInstanceSupplier());
/* 223 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<JwtDecoder> getJwtDecoderInstanceSupplier() {
/* 230 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "jwtDecoder", new Class[] { JWKSource.class
/* 231 */         }).withGenerator((paramRegisteredBean, paramAutowiredArguments) -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).jwtDecoder((JWKSource)paramAutowiredArguments.get(0)));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getJwtDecoderBeanDefinition() {
/* 238 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(JwtDecoder.class);
/* 239 */     rootBeanDefinition.setInstanceSupplier((Supplier)getJwtDecoderInstanceSupplier());
/* 240 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<TokenSettings> getTokenSettingsInstanceSupplier() {
/* 247 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "tokenSettings", new Class[0])
/* 248 */       .withGenerator(paramRegisteredBean -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).tokenSettings());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getTokenSettingsBeanDefinition() {
/* 255 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(TokenSettings.class);
/* 256 */     rootBeanDefinition.setInstanceSupplier((Supplier)getTokenSettingsInstanceSupplier());
/* 257 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static BeanInstanceSupplier<AuthorizationServerSettings> getAuthorizationServerSettingsInstanceSupplier() {
/* 265 */     return BeanInstanceSupplier.forFactoryMethod(SecurityConfig.class, "authorizationServerSettings", new Class[0])
/* 266 */       .withGenerator(paramRegisteredBean -> ((SecurityConfig)paramRegisteredBean.getBeanFactory().getBean(SecurityConfig.class)).authorizationServerSettings());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static BeanDefinition getAuthorizationServerSettingsBeanDefinition() {
/* 273 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(AuthorizationServerSettings.class);
/* 274 */     rootBeanDefinition.setInstanceSupplier((Supplier)getAuthorizationServerSettingsInstanceSupplier());
/* 275 */     return (BeanDefinition)rootBeanDefinition;
/*     */   }
/*     */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\config\SecurityConfig__BeanDefinitions.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */