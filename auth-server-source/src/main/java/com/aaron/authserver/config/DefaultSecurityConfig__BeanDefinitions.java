/*    */ package BOOT-INF.classes.com.aaron.authserver.config;
/*    */ 
/*    */ import com.aaron.authserver.config.DefaultSecurityConfig;
/*    */ import java.util.function.Supplier;
/*    */ import org.springframework.beans.factory.config.BeanDefinition;
/*    */ import org.springframework.beans.factory.support.RootBeanDefinition;
/*    */ import org.springframework.context.annotation.ConfigurationClassUtils;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DefaultSecurityConfig__BeanDefinitions
/*    */ {
/*    */   public static BeanDefinition getDefaultSecurityConfigBeanDefinition() {
/* 15 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(DefaultSecurityConfig.class);
/* 16 */     rootBeanDefinition.setTargetType(DefaultSecurityConfig.class);
/* 17 */     ConfigurationClassUtils.initializeConfigurationClass(DefaultSecurityConfig.class);
/* 18 */     rootBeanDefinition.setInstanceSupplier(null::new);
/* 19 */     return (BeanDefinition)rootBeanDefinition;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\config\DefaultSecurityConfig__BeanDefinitions.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */