package BOOT-INF.classes.com.aaron.authserver.config;

import com.aaron.authserver.authentication.dao.SaltPasswordEncoder;
import com.aaron.authserver.authentication.dao.SaltSource;
import com.aaron.authserver.authentication.dao.SaltedDaoAuthenticationProvider;
import com.aaron.authserver.config.SecurityConfig;
import com.aaron.authserver.config.SecurityConfig$$SpringCGLIB$;
import com.aaron.authserver.service.UserService;
import com.nimbusds.jose.jwk.source.JWKSource;
import java.lang.reflect.Method;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.cglib.core.ReflectUtils;
import org.springframework.cglib.core.Signature;
import org.springframework.cglib.proxy.Callback;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;
import org.springframework.cglib.proxy.NoOp;
import org.springframework.context.annotation.ConfigurationClassEnhancer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.oauth2.client.endpoint.OAuth2AccessTokenResponseClient;
import org.springframework.security.oauth2.client.web.AuthorizationRequestRepository;
import org.springframework.security.oauth2.client.web.HttpSessionOAuth2AuthorizedClientRepository;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;
import org.springframework.security.web.SecurityFilterChain;

public class null extends SecurityConfig implements ConfigurationClassEnhancer.EnhancedConfiguration {
  private boolean CGLIB$BOUND;
  
  public static Object CGLIB$FACTORY_DATA;
  
  private static final ThreadLocal CGLIB$THREAD_CALLBACKS;
  
  private static final Callback[] CGLIB$STATIC_CALLBACKS;
  
  private MethodInterceptor CGLIB$CALLBACK_0;
  
  private MethodInterceptor CGLIB$CALLBACK_1;
  
  private NoOp CGLIB$CALLBACK_2;
  
  private static Object CGLIB$CALLBACK_FILTER;
  
  private static final Method CGLIB$saltSource$0$Method;
  
  private static final MethodProxy CGLIB$saltSource$0$Proxy;
  
  private static final Object[] CGLIB$emptyArgs;
  
  private static final Method CGLIB$jwtDecoder$1$Method;
  
  private static final MethodProxy CGLIB$jwtDecoder$1$Proxy;
  
  private static final Method CGLIB$saltPasswordEncoder$2$Method;
  
  private static final MethodProxy CGLIB$saltPasswordEncoder$2$Proxy;
  
  private static final Method CGLIB$defaultSecurityFilterChain$3$Method;
  
  private static final MethodProxy CGLIB$defaultSecurityFilterChain$3$Proxy;
  
  private static final Method CGLIB$webSecurityCustomizer$4$Method;
  
  private static final MethodProxy CGLIB$webSecurityCustomizer$4$Proxy;
  
  private static final Method CGLIB$accessTokenResponseClient$5$Method;
  
  private static final MethodProxy CGLIB$accessTokenResponseClient$5$Proxy;
  
  private static final Method CGLIB$tokenCustomizer$6$Method;
  
  private static final MethodProxy CGLIB$tokenCustomizer$6$Proxy;
  
  private static final Method CGLIB$tokenSettings$7$Method;
  
  private static final MethodProxy CGLIB$tokenSettings$7$Proxy;
  
  private static final Method CGLIB$saltedDaoAuthenticationProvider$9$Method;
  
  private static final MethodProxy CGLIB$saltedDaoAuthenticationProvider$9$Proxy;
  
  private static final Method CGLIB$authorizationServerSecurityFilterChain$10$Method;
  
  private static final MethodProxy CGLIB$authorizationServerSecurityFilterChain$10$Proxy;
  
  private static final Method CGLIB$authorizationRequestRepository$11$Method;
  
  private static final MethodProxy CGLIB$authorizationRequestRepository$11$Proxy;
  
  private static final Method CGLIB$auth2AuthorizedClientRepository$12$Method;
  
  private static final MethodProxy CGLIB$auth2AuthorizedClientRepository$12$Proxy;
  
  private static final Method CGLIB$authorizationServerSettings$13$Method;
  
  private static final MethodProxy CGLIB$authorizationServerSettings$13$Proxy;
  
  private static final Method CGLIB$setBeanFactory$18$Method;
  
  private static final MethodProxy CGLIB$setBeanFactory$18$Proxy;
  
  public BeanFactory $$beanFactory;
  
  static void CGLIB$STATICHOOK5() {
    CGLIB$THREAD_CALLBACKS = new ThreadLocal();
    CGLIB$emptyArgs = new Object[0];
    Class<?> clazz1 = Class.forName("com.aaron.authserver.config.SecurityConfig$$SpringCGLIB$$0");
    Class<?> clazz2;
    CGLIB$setBeanFactory$18$Method = ReflectUtils.findMethods(new String[] { "setBeanFactory", "(Lorg/springframework/beans/factory/BeanFactory;)V" }, (clazz2 = Class.forName("org.springframework.beans.factory.BeanFactoryAware")).getDeclaredMethods())[0];
    CGLIB$setBeanFactory$18$Proxy = MethodProxy.create(clazz2, clazz1, "(Lorg/springframework/beans/factory/BeanFactory;)V", "setBeanFactory", "CGLIB$setBeanFactory$18");
    ReflectUtils.findMethods(new String[] { "setBeanFactory", "(Lorg/springframework/beans/factory/BeanFactory;)V" }, (clazz2 = Class.forName("org.springframework.beans.factory.BeanFactoryAware")).getDeclaredMethods());
    CGLIB$saltSource$0$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[0];
    CGLIB$saltSource$0$Proxy = MethodProxy.create(clazz2, clazz1, "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "saltSource", "CGLIB$saltSource$0");
    CGLIB$jwtDecoder$1$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[1];
    CGLIB$jwtDecoder$1$Proxy = MethodProxy.create(clazz2, clazz1, "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "jwtDecoder", "CGLIB$jwtDecoder$1");
    CGLIB$saltPasswordEncoder$2$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[2];
    CGLIB$saltPasswordEncoder$2$Proxy = MethodProxy.create(clazz2, clazz1, "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "saltPasswordEncoder", "CGLIB$saltPasswordEncoder$2");
    CGLIB$defaultSecurityFilterChain$3$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[3];
    CGLIB$defaultSecurityFilterChain$3$Proxy = MethodProxy.create(clazz2, clazz1, "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "defaultSecurityFilterChain", "CGLIB$defaultSecurityFilterChain$3");
    CGLIB$webSecurityCustomizer$4$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[4];
    CGLIB$webSecurityCustomizer$4$Proxy = MethodProxy.create(clazz2, clazz1, "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", "webSecurityCustomizer", "CGLIB$webSecurityCustomizer$4");
    CGLIB$accessTokenResponseClient$5$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[5];
    CGLIB$accessTokenResponseClient$5$Proxy = MethodProxy.create(clazz2, clazz1, "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "accessTokenResponseClient", "CGLIB$accessTokenResponseClient$5");
    CGLIB$tokenCustomizer$6$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[6];
    CGLIB$tokenCustomizer$6$Proxy = MethodProxy.create(clazz2, clazz1, "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenCustomizer", "CGLIB$tokenCustomizer$6");
    CGLIB$tokenSettings$7$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[7];
    CGLIB$tokenSettings$7$Proxy = MethodProxy.create(clazz2, clazz1, "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "tokenSettings", "CGLIB$tokenSettings$7");
    CGLIB$saltedDaoAuthenticationProvider$9$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[8];
    CGLIB$saltedDaoAuthenticationProvider$9$Proxy = MethodProxy.create(clazz2, clazz1, "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "saltedDaoAuthenticationProvider", "CGLIB$saltedDaoAuthenticationProvider$9");
    CGLIB$authorizationServerSecurityFilterChain$10$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[9];
    CGLIB$authorizationServerSecurityFilterChain$10$Proxy = MethodProxy.create(clazz2, clazz1, "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "authorizationServerSecurityFilterChain", "CGLIB$authorizationServerSecurityFilterChain$10");
    CGLIB$authorizationRequestRepository$11$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[10];
    CGLIB$authorizationRequestRepository$11$Proxy = MethodProxy.create(clazz2, clazz1, "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "authorizationRequestRepository", "CGLIB$authorizationRequestRepository$11");
    CGLIB$auth2AuthorizedClientRepository$12$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[11];
    CGLIB$auth2AuthorizedClientRepository$12$Proxy = MethodProxy.create(clazz2, clazz1, "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "auth2AuthorizedClientRepository", "CGLIB$auth2AuthorizedClientRepository$12");
    CGLIB$authorizationServerSettings$13$Method = ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods())[12];
    CGLIB$authorizationServerSettings$13$Proxy = MethodProxy.create(clazz2, clazz1, "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;", "authorizationServerSettings", "CGLIB$authorizationServerSettings$13");
    ReflectUtils.findMethods(new String[] { 
          "saltSource", "()Lcom/aaron/authserver/authentication/dao/SaltSource;", "jwtDecoder", "(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;", "saltPasswordEncoder", "()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;", "defaultSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", "webSecurityCustomizer", "()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;", 
          "accessTokenResponseClient", "()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;", "tokenCustomizer", "(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;", "tokenSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;", "saltedDaoAuthenticationProvider", "()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;", "authorizationServerSecurityFilterChain", "(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;", 
          "authorizationRequestRepository", "()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;", "auth2AuthorizedClientRepository", "()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;", "authorizationServerSettings", "()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;" }, (clazz2 = Class.forName("com.aaron.authserver.config.SecurityConfig")).getDeclaredMethods());
  }
  
  final SaltSource CGLIB$saltSource$0() {
    return super.saltSource();
  }
  
  public final SaltSource saltSource() {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (SaltSource)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$saltSource$0$Method, CGLIB$emptyArgs, CGLIB$saltSource$0$Proxy) : super.saltSource();
  }
  
  final JwtDecoder CGLIB$jwtDecoder$1(JWKSource paramJWKSource) {
    return super.jwtDecoder(paramJWKSource);
  }
  
  public final JwtDecoder jwtDecoder(JWKSource paramJWKSource) {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (JwtDecoder)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$jwtDecoder$1$Method, new Object[] { paramJWKSource }, CGLIB$jwtDecoder$1$Proxy) : super.jwtDecoder(paramJWKSource);
  }
  
  final SaltPasswordEncoder CGLIB$saltPasswordEncoder$2() {
    return super.saltPasswordEncoder();
  }
  
  public final SaltPasswordEncoder saltPasswordEncoder() {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (SaltPasswordEncoder)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$saltPasswordEncoder$2$Method, CGLIB$emptyArgs, CGLIB$saltPasswordEncoder$2$Proxy) : super.saltPasswordEncoder();
  }
  
  final SecurityFilterChain CGLIB$defaultSecurityFilterChain$3(HttpSecurity paramHttpSecurity) throws Exception {
    return super.defaultSecurityFilterChain(paramHttpSecurity);
  }
  
  final SecurityFilterChain defaultSecurityFilterChain(HttpSecurity paramHttpSecurity) throws Exception {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (SecurityFilterChain)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$defaultSecurityFilterChain$3$Method, new Object[] { paramHttpSecurity }, CGLIB$defaultSecurityFilterChain$3$Proxy) : super.defaultSecurityFilterChain(paramHttpSecurity);
  }
  
  final WebSecurityCustomizer CGLIB$webSecurityCustomizer$4() {
    return super.webSecurityCustomizer();
  }
  
  final WebSecurityCustomizer webSecurityCustomizer() {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (WebSecurityCustomizer)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$webSecurityCustomizer$4$Method, CGLIB$emptyArgs, CGLIB$webSecurityCustomizer$4$Proxy) : super.webSecurityCustomizer();
  }
  
  final OAuth2AccessTokenResponseClient CGLIB$accessTokenResponseClient$5() {
    return super.accessTokenResponseClient();
  }
  
  public final OAuth2AccessTokenResponseClient accessTokenResponseClient() {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (OAuth2AccessTokenResponseClient)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$accessTokenResponseClient$5$Method, CGLIB$emptyArgs, CGLIB$accessTokenResponseClient$5$Proxy) : super.accessTokenResponseClient();
  }
  
  final OAuth2TokenCustomizer CGLIB$tokenCustomizer$6(UserService paramUserService) {
    return super.tokenCustomizer(paramUserService);
  }
  
  final OAuth2TokenCustomizer tokenCustomizer(UserService paramUserService) {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (OAuth2TokenCustomizer)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$tokenCustomizer$6$Method, new Object[] { paramUserService }, CGLIB$tokenCustomizer$6$Proxy) : super.tokenCustomizer(paramUserService);
  }
  
  final TokenSettings CGLIB$tokenSettings$7() {
    return super.tokenSettings();
  }
  
  public final TokenSettings tokenSettings() {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (TokenSettings)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$tokenSettings$7$Method, CGLIB$emptyArgs, CGLIB$tokenSettings$7$Proxy) : super.tokenSettings();
  }
  
  final SaltedDaoAuthenticationProvider CGLIB$saltedDaoAuthenticationProvider$9() {
    return super.saltedDaoAuthenticationProvider();
  }
  
  public final SaltedDaoAuthenticationProvider saltedDaoAuthenticationProvider() {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (SaltedDaoAuthenticationProvider)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$saltedDaoAuthenticationProvider$9$Method, CGLIB$emptyArgs, CGLIB$saltedDaoAuthenticationProvider$9$Proxy) : super.saltedDaoAuthenticationProvider();
  }
  
  final SecurityFilterChain CGLIB$authorizationServerSecurityFilterChain$10(HttpSecurity paramHttpSecurity) throws Exception {
    return super.authorizationServerSecurityFilterChain(paramHttpSecurity);
  }
  
  final SecurityFilterChain authorizationServerSecurityFilterChain(HttpSecurity paramHttpSecurity) throws Exception {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (SecurityFilterChain)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$authorizationServerSecurityFilterChain$10$Method, new Object[] { paramHttpSecurity }, CGLIB$authorizationServerSecurityFilterChain$10$Proxy) : super.authorizationServerSecurityFilterChain(paramHttpSecurity);
  }
  
  final AuthorizationRequestRepository CGLIB$authorizationRequestRepository$11() {
    return super.authorizationRequestRepository();
  }
  
  public final AuthorizationRequestRepository authorizationRequestRepository() {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (AuthorizationRequestRepository)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$authorizationRequestRepository$11$Method, CGLIB$emptyArgs, CGLIB$authorizationRequestRepository$11$Proxy) : super.authorizationRequestRepository();
  }
  
  final HttpSessionOAuth2AuthorizedClientRepository CGLIB$auth2AuthorizedClientRepository$12() {
    return super.auth2AuthorizedClientRepository();
  }
  
  public final HttpSessionOAuth2AuthorizedClientRepository auth2AuthorizedClientRepository() {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (HttpSessionOAuth2AuthorizedClientRepository)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$auth2AuthorizedClientRepository$12$Method, CGLIB$emptyArgs, CGLIB$auth2AuthorizedClientRepository$12$Proxy) : super.auth2AuthorizedClientRepository();
  }
  
  final AuthorizationServerSettings CGLIB$authorizationServerSettings$13() {
    return super.authorizationServerSettings();
  }
  
  public final AuthorizationServerSettings authorizationServerSettings() {
    if (this.CGLIB$CALLBACK_0 == null)
      CGLIB$BIND_CALLBACKS(this); 
    return (this.CGLIB$CALLBACK_0 != null) ? (AuthorizationServerSettings)this.CGLIB$CALLBACK_0.intercept(this, CGLIB$authorizationServerSettings$13$Method, CGLIB$emptyArgs, CGLIB$authorizationServerSettings$13$Proxy) : super.authorizationServerSettings();
  }
  
  final void CGLIB$setBeanFactory$18(BeanFactory paramBeanFactory) throws BeansException {
    super.setBeanFactory(paramBeanFactory);
  }
  
  public final void setBeanFactory(BeanFactory paramBeanFactory) throws BeansException {
    if (this.CGLIB$CALLBACK_1 == null)
      CGLIB$BIND_CALLBACKS(this); 
    if (this.CGLIB$CALLBACK_1 != null) {
      (new Object[1])[0] = paramBeanFactory;
      return;
    } 
    super.setBeanFactory(paramBeanFactory);
  }
  
  public static MethodProxy CGLIB$findMethodProxy(Signature paramSignature) {
    // Byte code:
    //   0: aload_0
    //   1: invokevirtual toString : ()Ljava/lang/String;
    //   4: dup
    //   5: invokevirtual hashCode : ()I
    //   8: lookupswitch default -> 310, -1779680884 -> 132, -1544317662 -> 144, -1498464490 -> 156, -989488491 -> 168, -935224611 -> 180, 141265702 -> 193, 444213649 -> 206, 652659989 -> 219, 714535052 -> 232, 903702368 -> 245, 1556068820 -> 258, 1621036355 -> 271, 1948665758 -> 284, 2095635076 -> 297
    //   132: ldc 'defaultSecurityFilterChain(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;'
    //   134: invokevirtual equals : (Ljava/lang/Object;)Z
    //   137: ifeq -> 311
    //   140: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$defaultSecurityFilterChain$3$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   143: areturn
    //   144: ldc 'auth2AuthorizedClientRepository()Lorg/springframework/security/oauth2/client/web/HttpSessionOAuth2AuthorizedClientRepository;'
    //   146: invokevirtual equals : (Ljava/lang/Object;)Z
    //   149: ifeq -> 311
    //   152: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$auth2AuthorizedClientRepository$12$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   155: areturn
    //   156: ldc 'saltedDaoAuthenticationProvider()Lcom/aaron/authserver/authentication/dao/SaltedDaoAuthenticationProvider;'
    //   158: invokevirtual equals : (Ljava/lang/Object;)Z
    //   161: ifeq -> 311
    //   164: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$saltedDaoAuthenticationProvider$9$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   167: areturn
    //   168: ldc 'jwtDecoder(Lcom/nimbusds/jose/jwk/source/JWKSource;)Lorg/springframework/security/oauth2/jwt/JwtDecoder;'
    //   170: invokevirtual equals : (Ljava/lang/Object;)Z
    //   173: ifeq -> 311
    //   176: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$jwtDecoder$1$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   179: areturn
    //   180: ldc_w 'webSecurityCustomizer()Lorg/springframework/security/config/annotation/web/configuration/WebSecurityCustomizer;'
    //   183: invokevirtual equals : (Ljava/lang/Object;)Z
    //   186: ifeq -> 311
    //   189: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$webSecurityCustomizer$4$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   192: areturn
    //   193: ldc_w 'tokenSettings()Lorg/springframework/security/oauth2/server/authorization/settings/TokenSettings;'
    //   196: invokevirtual equals : (Ljava/lang/Object;)Z
    //   199: ifeq -> 311
    //   202: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$tokenSettings$7$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   205: areturn
    //   206: ldc_w 'authorizationServerSecurityFilterChain(Lorg/springframework/security/config/annotation/web/builders/HttpSecurity;)Lorg/springframework/security/web/SecurityFilterChain;'
    //   209: invokevirtual equals : (Ljava/lang/Object;)Z
    //   212: ifeq -> 311
    //   215: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$authorizationServerSecurityFilterChain$10$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   218: areturn
    //   219: ldc_w 'authorizationRequestRepository()Lorg/springframework/security/oauth2/client/web/AuthorizationRequestRepository;'
    //   222: invokevirtual equals : (Ljava/lang/Object;)Z
    //   225: ifeq -> 311
    //   228: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$authorizationRequestRepository$11$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   231: areturn
    //   232: ldc_w 'saltPasswordEncoder()Lcom/aaron/authserver/authentication/dao/SaltPasswordEncoder;'
    //   235: invokevirtual equals : (Ljava/lang/Object;)Z
    //   238: ifeq -> 311
    //   241: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$saltPasswordEncoder$2$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   244: areturn
    //   245: ldc_w 'authorizationServerSettings()Lorg/springframework/security/oauth2/server/authorization/settings/AuthorizationServerSettings;'
    //   248: invokevirtual equals : (Ljava/lang/Object;)Z
    //   251: ifeq -> 311
    //   254: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$authorizationServerSettings$13$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   257: areturn
    //   258: ldc_w 'saltSource()Lcom/aaron/authserver/authentication/dao/SaltSource;'
    //   261: invokevirtual equals : (Ljava/lang/Object;)Z
    //   264: ifeq -> 311
    //   267: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$saltSource$0$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   270: areturn
    //   271: ldc_w 'accessTokenResponseClient()Lorg/springframework/security/oauth2/client/endpoint/OAuth2AccessTokenResponseClient;'
    //   274: invokevirtual equals : (Ljava/lang/Object;)Z
    //   277: ifeq -> 311
    //   280: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$accessTokenResponseClient$5$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   283: areturn
    //   284: ldc_w 'tokenCustomizer(Lcom/aaron/authserver/service/UserService;)Lorg/springframework/security/oauth2/server/authorization/token/OAuth2TokenCustomizer;'
    //   287: invokevirtual equals : (Ljava/lang/Object;)Z
    //   290: ifeq -> 311
    //   293: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$tokenCustomizer$6$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   296: areturn
    //   297: ldc_w 'setBeanFactory(Lorg/springframework/beans/factory/BeanFactory;)V'
    //   300: invokevirtual equals : (Ljava/lang/Object;)Z
    //   303: ifeq -> 311
    //   306: getstatic com/aaron/authserver/config/SecurityConfig$$SpringCGLIB$$0.CGLIB$setBeanFactory$18$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   309: areturn
    //   310: pop
    //   311: aconst_null
    //   312: areturn
  }
  
  public null() {
    CGLIB$BIND_CALLBACKS(this);
  }
  
  public static void CGLIB$SET_THREAD_CALLBACKS(Callback[] paramArrayOfCallback) {
    CGLIB$THREAD_CALLBACKS.set(paramArrayOfCallback);
  }
  
  public static void CGLIB$SET_STATIC_CALLBACKS(Callback[] paramArrayOfCallback) {
    CGLIB$STATIC_CALLBACKS = paramArrayOfCallback;
  }
  
  private static final void CGLIB$BIND_CALLBACKS(Object paramObject) {
    null  = (null)paramObject;
    if (!.CGLIB$BOUND) {
      .CGLIB$BOUND = true;
      if (CGLIB$THREAD_CALLBACKS.get() == null) {
        CGLIB$THREAD_CALLBACKS.get();
        if (CGLIB$STATIC_CALLBACKS == null)
          return; 
      } 
      .CGLIB$CALLBACK_2 = (NoOp)((Callback[])CGLIB$THREAD_CALLBACKS.get())[2];
      .CGLIB$CALLBACK_1 = (MethodInterceptor)((Callback[])CGLIB$THREAD_CALLBACKS.get())[1];
      .CGLIB$CALLBACK_0 = (MethodInterceptor)((Callback[])CGLIB$THREAD_CALLBACKS.get())[0];
    } 
  }
  
  static {
    CGLIB$STATICHOOK6();
    CGLIB$STATICHOOK5();
  }
  
  static void CGLIB$STATICHOOK6() {}
}


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\config\SecurityConfig$$SpringCGLIB$$0.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */