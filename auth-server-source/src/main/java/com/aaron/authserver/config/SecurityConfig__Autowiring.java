/*    */ package BOOT-INF.classes.com.aaron.authserver.config;
/*    */ 
/*    */ import com.aaron.authserver.config.SecurityConfig;
/*    */ import org.springframework.beans.factory.aot.AutowiredFieldValueResolver;
/*    */ import org.springframework.beans.factory.support.RegisteredBean;
/*    */ import org.springframework.security.core.userdetails.UserDetailsService;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SecurityConfig__Autowiring
/*    */ {
/*    */   public static SecurityConfig apply(RegisteredBean paramRegisteredBean, SecurityConfig paramSecurityConfig) {
/* 14 */     paramSecurityConfig.userDetailsService = (UserDetailsService)AutowiredFieldValueResolver.forRequiredField("userDetailsService").resolve(paramRegisteredBean);
/* 15 */     return paramSecurityConfig;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\config\SecurityConfig__Autowiring.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */