package BOOT-INF.classes.com.aaron.authserver.config;

import com.aaron.authserver.config.DefaultSecurityConfig;
import com.aaron.authserver.config.DefaultSecurityConfig$$SpringCGLIB$;
import java.lang.reflect.Method;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.cglib.core.ReflectUtils;
import org.springframework.cglib.core.Signature;
import org.springframework.cglib.proxy.Callback;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;
import org.springframework.cglib.proxy.NoOp;
import org.springframework.context.annotation.ConfigurationClassEnhancer;

public class null extends DefaultSecurityConfig implements ConfigurationClassEnhancer.EnhancedConfiguration {
  private boolean CGLIB$BOUND;
  
  public static Object CGLIB$FACTORY_DATA;
  
  private static final ThreadLocal CGLIB$THREAD_CALLBACKS;
  
  private static final Callback[] CGLIB$STATIC_CALLBACKS;
  
  private MethodInterceptor CGLIB$CALLBACK_0;
  
  private MethodInterceptor CGLIB$CALLBACK_1;
  
  private NoOp CGLIB$CALLBACK_2;
  
  private static Object CGLIB$CALLBACK_FILTER;
  
  private static final Method CGLIB$setBeanFactory$5$Method;
  
  private static final MethodProxy CGLIB$setBeanFactory$5$Proxy;
  
  private static final Object[] CGLIB$emptyArgs;
  
  public BeanFactory $$beanFactory;
  
  static void CGLIB$STATICHOOK3() {
    CGLIB$THREAD_CALLBACKS = new ThreadLocal();
    CGLIB$emptyArgs = new Object[0];
    Class<?> clazz1 = Class.forName("com.aaron.authserver.config.DefaultSecurityConfig$$SpringCGLIB$$0");
    Class<?> clazz2;
    CGLIB$setBeanFactory$5$Method = ReflectUtils.findMethods(new String[] { "setBeanFactory", "(Lorg/springframework/beans/factory/BeanFactory;)V" }, (clazz2 = Class.forName("org.springframework.beans.factory.BeanFactoryAware")).getDeclaredMethods())[0];
    CGLIB$setBeanFactory$5$Proxy = MethodProxy.create(clazz2, clazz1, "(Lorg/springframework/beans/factory/BeanFactory;)V", "setBeanFactory", "CGLIB$setBeanFactory$5");
    ReflectUtils.findMethods(new String[] { "setBeanFactory", "(Lorg/springframework/beans/factory/BeanFactory;)V" }, (clazz2 = Class.forName("org.springframework.beans.factory.BeanFactoryAware")).getDeclaredMethods());
  }
  
  final void CGLIB$setBeanFactory$5(BeanFactory paramBeanFactory) throws BeansException {
    super.setBeanFactory(paramBeanFactory);
  }
  
  public final void setBeanFactory(BeanFactory paramBeanFactory) throws BeansException {
    if (this.CGLIB$CALLBACK_1 == null)
      CGLIB$BIND_CALLBACKS(this); 
    if (this.CGLIB$CALLBACK_1 != null) {
      (new Object[1])[0] = paramBeanFactory;
      return;
    } 
    super.setBeanFactory(paramBeanFactory);
  }
  
  public static MethodProxy CGLIB$findMethodProxy(Signature paramSignature) {
    // Byte code:
    //   0: aload_0
    //   1: invokevirtual toString : ()Ljava/lang/String;
    //   4: dup
    //   5: invokevirtual hashCode : ()I
    //   8: tableswitch default -> 40, 2095635076 -> 28
    //   28: ldc 'setBeanFactory(Lorg/springframework/beans/factory/BeanFactory;)V'
    //   30: invokevirtual equals : (Ljava/lang/Object;)Z
    //   33: ifeq -> 41
    //   36: getstatic com/aaron/authserver/config/DefaultSecurityConfig$$SpringCGLIB$$0.CGLIB$setBeanFactory$5$Proxy : Lorg/springframework/cglib/proxy/MethodProxy;
    //   39: areturn
    //   40: pop
    //   41: aconst_null
    //   42: areturn
  }
  
  public null() {
    CGLIB$BIND_CALLBACKS(this);
  }
  
  public static void CGLIB$SET_THREAD_CALLBACKS(Callback[] paramArrayOfCallback) {
    CGLIB$THREAD_CALLBACKS.set(paramArrayOfCallback);
  }
  
  public static void CGLIB$SET_STATIC_CALLBACKS(Callback[] paramArrayOfCallback) {
    CGLIB$STATIC_CALLBACKS = paramArrayOfCallback;
  }
  
  private static final void CGLIB$BIND_CALLBACKS(Object paramObject) {
    null  = (null)paramObject;
    if (!.CGLIB$BOUND) {
      .CGLIB$BOUND = true;
      if (CGLIB$THREAD_CALLBACKS.get() == null) {
        CGLIB$THREAD_CALLBACKS.get();
        if (CGLIB$STATIC_CALLBACKS == null)
          return; 
      } 
      .CGLIB$CALLBACK_2 = (NoOp)((Callback[])CGLIB$THREAD_CALLBACKS.get())[2];
      .CGLIB$CALLBACK_1 = (MethodInterceptor)((Callback[])CGLIB$THREAD_CALLBACKS.get())[1];
      .CGLIB$CALLBACK_0 = (MethodInterceptor)((Callback[])CGLIB$THREAD_CALLBACKS.get())[0];
    } 
  }
  
  static {
    CGLIB$STATICHOOK4();
    CGLIB$STATICHOOK3();
  }
  
  static void CGLIB$STATICHOOK4() {}
}


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\config\DefaultSecurityConfig$$SpringCGLIB$$0.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */