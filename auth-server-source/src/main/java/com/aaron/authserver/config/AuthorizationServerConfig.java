package com.aaron.authserver.config;
/*     */ 
/*     */ import com.aaron.authserver.federation.FederatedIdentityAuthenticationSuccessHandler;
/*     */ import com.nimbusds.jose.KeySourceException;
/*     */ import com.nimbusds.jose.jwk.JWK;
/*     */ import com.nimbusds.jose.jwk.JWKSelector;
/*     */ import com.nimbusds.jose.jwk.JWKSet;
/*     */ import com.nimbusds.jose.jwk.RSAKey;
/*     */ import com.nimbusds.jose.jwk.source.JWKSource;
/*     */ import com.nimbusds.jose.proc.SecurityContext;
/*     */ import java.security.KeyPair;
/*     */ import java.security.KeyPairGenerator;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.interfaces.RSAPrivateKey;
/*     */ import java.security.interfaces.RSAPublicKey;
/*     */ import java.util.List;
/*     */ import java.util.Set;
/*     */ import java.util.UUID;
/*     */ import java.util.stream.Collectors;
/*     */ import org.springframework.core.annotation.Order;
/*     */ import org.springframework.http.MediaType;
/*     */ import org.springframework.security.config.Customizer;
/*     */ import org.springframework.security.config.annotation.web.builders.HttpSecurity;
/*     */ import org.springframework.security.config.annotation.web.configurers.ExceptionHandlingConfigurer;
/*     */ import org.springframework.security.config.annotation.web.configurers.oauth2.server.resource.OAuth2ResourceServerConfigurer;
/*     */ import org.springframework.security.core.Authentication;
/*     */ import org.springframework.security.core.GrantedAuthority;
/*     */ import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
/*     */ import org.springframework.security.oauth2.core.AuthorizationGrantType;
/*     */ import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
/*     */ import org.springframework.security.oauth2.jwt.JwtDecoder;
/*     */ import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
/*     */ import org.springframework.security.oauth2.server.authorization.client.InMemoryRegisteredClientRepository;
/*     */ import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
/*     */ import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
/*     */ import org.springframework.security.oauth2.server.authorization.config.annotation.web.configuration.OAuth2AuthorizationServerConfiguration;
/*     */ import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationServerConfigurer;
/*     */ import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
/*     */ import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
/*     */ import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
/*     */ import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;
/*     */ import org.springframework.security.web.AuthenticationEntryPoint;
/*     */ import org.springframework.security.web.SecurityFilterChain;
/*     */ import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
/*     */ import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;
/*     */ import org.springframework.security.web.util.matcher.MediaTypeRequestMatcher;
/*     */ import org.springframework.security.web.util.matcher.RequestMatcher;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AuthorizationServerConfig
/*     */ {
/*     */   private BCryptPasswordEncoder passwordEncoder;
/*     */   
/*     */   @Order(-2147483648)
/*     */   public SecurityFilterChain authServerSecurityFilterChain(HttpSecurity http) throws Exception {
     OAuth2AuthorizationServerConfiguration.applyDefaultSecurity(http);
     ((OAuth2AuthorizationServerConfigurer)http.getConfigurer(OAuth2AuthorizationServerConfigurer.class))
       .oidc(Customizer.withDefaults());
     http
/*     */ 
/*     */       
       .exceptionHandling(exceptions -> exceptions.defaultAuthenticationEntryPointFor((AuthenticationEntryPoint)new LoginUrlAuthenticationEntryPoint("/login"), (RequestMatcher)new MediaTypeRequestMatcher(new MediaType[] {
/*     */ 
/*     */               
/*     */               MediaType.TEXT_HTML
/*     */ 
/*     */ 
/*     */ 
/*     */             
             }))).oauth2ResourceServer(resourceServer -> resourceServer.jwt(Customizer.withDefaults()));
/*     */     
     return (SecurityFilterChain)http.build();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private AuthenticationSuccessHandler authenticationSuccessHandler() {
     return (AuthenticationSuccessHandler)new FederatedIdentityAuthenticationSuccessHandler();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public RegisteredClientRepository registeredClientRepository() {
     RegisteredClient registeredClient = RegisteredClient.withId(UUID.randomUUID().toString()).clientId("oauth-client").clientSecret("{noop}oauth-secret").clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC).authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE).authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS).authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN).redirectUri("https://ebook.endao.co/").scope("openid").scope("profile").scope("email").scope("phone").scope("address").scope("articles.read").clientSettings(ClientSettings.builder().requireAuthorizationConsent(true).build()).build();
     return (RegisteredClientRepository)new InMemoryRegisteredClientRepository(new RegisteredClient[] { registeredClient });
/*     */   }
/*     */ 
/*     */   
/*     */   public JWKSource<SecurityContext> jwkSource() throws NoSuchAlgorithmException {
     RSAKey rsaKey = generateRsa();
     JWKSet jwkSet = new JWKSet((JWK)rsaKey);
     return (jwkSelector, securityContext) -> jwkSelector.select(jwkSet);
/*     */   }
/*     */   
/*     */   private static RSAKey generateRsa() throws NoSuchAlgorithmException {
     KeyPair keyPair = generateRsaKey();
     RSAPublicKey publicKey = (RSAPublicKey)keyPair.getPublic();
     RSAPrivateKey privateKey = (RSAPrivateKey)keyPair.getPrivate();
     return (new RSAKey.Builder(publicKey))
       .privateKey(privateKey)
       .keyID(UUID.randomUUID().toString())
       .build();
/*     */   }
/*     */   
/*     */   private static KeyPair generateRsaKey() throws NoSuchAlgorithmException {
     KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
     keyPairGenerator.initialize(2048);
     return keyPairGenerator.generateKeyPair();
/*     */   }
/*     */ 
/*     */   
/*     */   public JwtDecoder jwtDecoder(JWKSource<SecurityContext> jwkSource) {
     return OAuth2AuthorizationServerConfiguration.jwtDecoder(jwkSource);
/*     */   }
/*     */ 
/*     */   
/*     */   public OAuth2TokenCustomizer<JwtEncodingContext> jwtCustomizer() {
     return context -> {
/*     */         if (context.getTokenType() == OAuth2TokenType.ACCESS_TOKEN) {
/*     */           Authentication principal = context.getPrincipal();
/*     */           Set<String> authorities = (Set<String>)principal.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toSet());
/*     */           for (String s : authorities) {
/*     */             System.out.println(s);
/*     */           }
/*     */           context.getClaims().claim("roles", authorities);
/*     */         } 
/*     */       };
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AuthorizationServerSettings authorizationServerSettings() {
     return AuthorizationServerSettings.builder().build();
/*     */   }
/*     */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\config\AuthorizationServerConfig.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
