/*     */ package BOOT-INF.classes.com.aaron.authserver.config;
/*     */ 
/*     */ import com.aaron.authserver.federation.FederatedIdentityAuthenticationSuccessHandler;
/*     */ import com.nimbusds.jose.KeySourceException;
/*     */ import com.nimbusds.jose.jwk.JWK;
/*     */ import com.nimbusds.jose.jwk.JWKSelector;
/*     */ import com.nimbusds.jose.jwk.JWKSet;
/*     */ import com.nimbusds.jose.jwk.RSAKey;
/*     */ import com.nimbusds.jose.jwk.source.JWKSource;
/*     */ import com.nimbusds.jose.proc.SecurityContext;
/*     */ import java.security.KeyPair;
/*     */ import java.security.KeyPairGenerator;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.interfaces.RSAPrivateKey;
/*     */ import java.security.interfaces.RSAPublicKey;
/*     */ import java.util.List;
/*     */ import java.util.Set;
/*     */ import java.util.UUID;
/*     */ import java.util.stream.Collectors;
/*     */ import org.springframework.core.annotation.Order;
/*     */ import org.springframework.http.MediaType;
/*     */ import org.springframework.security.config.Customizer;
/*     */ import org.springframework.security.config.annotation.web.builders.HttpSecurity;
/*     */ import org.springframework.security.config.annotation.web.configurers.ExceptionHandlingConfigurer;
/*     */ import org.springframework.security.config.annotation.web.configurers.oauth2.server.resource.OAuth2ResourceServerConfigurer;
/*     */ import org.springframework.security.core.Authentication;
/*     */ import org.springframework.security.core.GrantedAuthority;
/*     */ import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
/*     */ import org.springframework.security.oauth2.core.AuthorizationGrantType;
/*     */ import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
/*     */ import org.springframework.security.oauth2.jwt.JwtDecoder;
/*     */ import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
/*     */ import org.springframework.security.oauth2.server.authorization.client.InMemoryRegisteredClientRepository;
/*     */ import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
/*     */ import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
/*     */ import org.springframework.security.oauth2.server.authorization.config.annotation.web.configuration.OAuth2AuthorizationServerConfiguration;
/*     */ import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationServerConfigurer;
/*     */ import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
/*     */ import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
/*     */ import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
/*     */ import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;
/*     */ import org.springframework.security.web.AuthenticationEntryPoint;
/*     */ import org.springframework.security.web.SecurityFilterChain;
/*     */ import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
/*     */ import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;
/*     */ import org.springframework.security.web.util.matcher.MediaTypeRequestMatcher;
/*     */ import org.springframework.security.web.util.matcher.RequestMatcher;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AuthorizationServerConfig
/*     */ {
/*     */   private BCryptPasswordEncoder passwordEncoder;
/*     */   
/*     */   @Order(-2147483648)
/*     */   public SecurityFilterChain authServerSecurityFilterChain(HttpSecurity http) throws Exception {
/*  61 */     OAuth2AuthorizationServerConfiguration.applyDefaultSecurity(http);
/*  62 */     ((OAuth2AuthorizationServerConfigurer)http.getConfigurer(OAuth2AuthorizationServerConfigurer.class))
/*  63 */       .oidc(Customizer.withDefaults());
/*  64 */     http
/*     */ 
/*     */       
/*  67 */       .exceptionHandling(exceptions -> exceptions.defaultAuthenticationEntryPointFor((AuthenticationEntryPoint)new LoginUrlAuthenticationEntryPoint("/login"), (RequestMatcher)new MediaTypeRequestMatcher(new MediaType[] {
/*     */ 
/*     */               
/*     */               MediaType.TEXT_HTML
/*     */ 
/*     */ 
/*     */ 
/*     */             
/*  75 */             }))).oauth2ResourceServer(resourceServer -> resourceServer.jwt(Customizer.withDefaults()));
/*     */     
/*  77 */     return (SecurityFilterChain)http.build();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private AuthenticationSuccessHandler authenticationSuccessHandler() {
/* 100 */     return (AuthenticationSuccessHandler)new FederatedIdentityAuthenticationSuccessHandler();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public RegisteredClientRepository registeredClientRepository() {
/* 117 */     RegisteredClient registeredClient = RegisteredClient.withId(UUID.randomUUID().toString()).clientId("oauth-client").clientSecret("{noop}oauth-secret").clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC).authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE).authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS).authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN).redirectUri("https://ebook.endao.co/").scope("openid").scope("profile").scope("email").scope("phone").scope("address").scope("articles.read").clientSettings(ClientSettings.builder().requireAuthorizationConsent(true).build()).build();
/* 118 */     return (RegisteredClientRepository)new InMemoryRegisteredClientRepository(new RegisteredClient[] { registeredClient });
/*     */   }
/*     */ 
/*     */   
/*     */   public JWKSource<SecurityContext> jwkSource() throws NoSuchAlgorithmException {
/* 123 */     RSAKey rsaKey = generateRsa();
/* 124 */     JWKSet jwkSet = new JWKSet((JWK)rsaKey);
/* 125 */     return (jwkSelector, securityContext) -> jwkSelector.select(jwkSet);
/*     */   }
/*     */   
/*     */   private static RSAKey generateRsa() throws NoSuchAlgorithmException {
/* 129 */     KeyPair keyPair = generateRsaKey();
/* 130 */     RSAPublicKey publicKey = (RSAPublicKey)keyPair.getPublic();
/* 131 */     RSAPrivateKey privateKey = (RSAPrivateKey)keyPair.getPrivate();
/* 132 */     return (new RSAKey.Builder(publicKey))
/* 133 */       .privateKey(privateKey)
/* 134 */       .keyID(UUID.randomUUID().toString())
/* 135 */       .build();
/*     */   }
/*     */   
/*     */   private static KeyPair generateRsaKey() throws NoSuchAlgorithmException {
/* 139 */     KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
/* 140 */     keyPairGenerator.initialize(2048);
/* 141 */     return keyPairGenerator.generateKeyPair();
/*     */   }
/*     */ 
/*     */   
/*     */   public JwtDecoder jwtDecoder(JWKSource<SecurityContext> jwkSource) {
/* 146 */     return OAuth2AuthorizationServerConfiguration.jwtDecoder(jwkSource);
/*     */   }
/*     */ 
/*     */   
/*     */   public OAuth2TokenCustomizer<JwtEncodingContext> jwtCustomizer() {
/* 151 */     return context -> {
/*     */         if (context.getTokenType() == OAuth2TokenType.ACCESS_TOKEN) {
/*     */           Authentication principal = context.getPrincipal();
/*     */           Set<String> authorities = (Set<String>)principal.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toSet());
/*     */           for (String s : authorities) {
/*     */             System.out.println(s);
/*     */           }
/*     */           context.getClaims().claim("roles", authorities);
/*     */         } 
/*     */       };
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AuthorizationServerSettings authorizationServerSettings() {
/* 167 */     return AuthorizationServerSettings.builder().build();
/*     */   }
/*     */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\config\AuthorizationServerConfig.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */