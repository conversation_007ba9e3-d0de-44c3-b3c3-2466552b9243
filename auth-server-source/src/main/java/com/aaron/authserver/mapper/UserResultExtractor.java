package com.aaron.authserver.mapper;
/*    */ 
/*    */ import com.aaron.authserver.model.User_Authority;
/*    */ import java.sql.ResultSet;
/*    */ import java.sql.SQLException;
/*    */ import java.util.ArrayList;
/*    */ import java.util.LinkedHashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import org.springframework.dao.DataAccessException;
/*    */ import org.springframework.jdbc.core.ResultSetExtractor;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class UserResultExtractor
/*    */   implements ResultSetExtractor<List<User_Authority>>
/*    */ {
/*    */   public List<User_Authority> extractData(ResultSet rs) throws SQLException, DataAccessException {
     Map<String, User_Authority> users = new LinkedHashMap<>();
     while (rs.next()) {
       String username = rs.getString("username");
       User_Authority user = users.get(username);
       if (user == null) {
/*    */ 
/*    */ 
/*    */         
         user = new User_Authority(username, rs.getString("password"), Boolean.valueOf(rs.getBoolean("enabled")), rs.getString("test"), new ArrayList());
/*    */         
         users.put(username, user);
/*    */       } 
       user.authority().add(rs.getString("authority"));
/*    */     } 
     return new ArrayList<>(users.values());
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\mapper\UserResultExtractor.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
