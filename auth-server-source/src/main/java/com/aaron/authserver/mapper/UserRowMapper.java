/*    */ package BOOT-INF.classes.com.aaron.authserver.mapper;
/*    */ 
/*    */ import com.aaron.authserver.model.User;
/*    */ import java.sql.ResultSet;
/*    */ import java.sql.SQLException;
/*    */ import org.springframework.jdbc.core.RowMapper;
/*    */ 
/*    */ public class UserRowMapper
/*    */   implements RowMapper<User>
/*    */ {
/* 11 */   public static final Byte BYTE_VALUE_1 = Byte.valueOf("1");
/*    */ 
/*    */   
/*    */   public User mapRow(ResultSet rs, int rowNum) throws SQLException {
/* 15 */     String firstName = rs.getString("firstname");
/* 16 */     String lastName = rs.getString("lastname");
/* 17 */     String username = firstName + firstName;
/* 18 */     String email = rs.getString("email");
/* 19 */     String password = rs.getString("password");
/* 20 */     String salt = rs.getString("salt");
/* 21 */     Byte approved = Byte.valueOf(rs.getByte("approved"));
/* 22 */     boolean isApproved = approved.equals(BYTE_VALUE_1);
/* 23 */     return new User(username, firstName, lastName, email, password, salt, Boolean.valueOf(isApproved));
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\mapper\UserRowMapper.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */