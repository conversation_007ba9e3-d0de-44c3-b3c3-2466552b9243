/*    */ package com.aaron.authserver.mapper;
/*    */ 
/*    */ import com.aaron.authserver.model.User;
/*    */ import java.sql.ResultSet;
/*    */ import java.sql.SQLException;
/*    */ import org.springframework.jdbc.core.RowMapper;
/*    */ 
/*    */ public class UserRowMapper
/*    */   implements RowMapper<User>
/*    */ {
   public static final Byte BYTE_VALUE_1 = Byte.valueOf("1");
/*    */ 
/*    */   
/*    */   public User mapRow(ResultSet rs, int rowNum) throws SQLException {
     String firstName = rs.getString("firstname");
     String lastName = rs.getString("lastname");
     String username = firstName + firstName;
     String email = rs.getString("email");
     String password = rs.getString("password");
     String salt = rs.getString("salt");
     Byte approved = Byte.valueOf(rs.getByte("approved"));
     boolean isApproved = approved.equals(BYTE_VALUE_1);
     return new User(username, firstName, lastName, email, password, salt, Boolean.valueOf(isApproved));
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\mapper\UserRowMapper.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
