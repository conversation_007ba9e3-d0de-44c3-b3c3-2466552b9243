/*    */ package BOOT-INF.classes.com.aaron.authserver.mapper;
/*    */ 
/*    */ import com.aaron.authserver.model.UserTest;
/*    */ import java.sql.ResultSet;
/*    */ import java.sql.SQLException;
/*    */ import org.springframework.jdbc.core.RowMapper;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class UserTestRowMapper
/*    */   implements RowMapper<UserTest>
/*    */ {
/*    */   public UserTest mapRow(ResultSet rs, int rowNum) throws SQLException {
/* 14 */     return new UserTest(rs.getString("username"), rs.getString("password"), Boolean.valueOf(rs.getBoolean("enabled")), rs.getString("test"));
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\mapper\UserTestRowMapper.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */