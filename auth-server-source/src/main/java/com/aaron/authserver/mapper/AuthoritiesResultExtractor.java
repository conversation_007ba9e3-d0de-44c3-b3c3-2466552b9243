/*    */ package BOOT-INF.classes.com.aaron.authserver.mapper;
/*    */ 
/*    */ import com.aaron.authserver.model.Authorities;
/*    */ import com.aaron.authserver.model.UserTest;
/*    */ import java.sql.ResultSet;
/*    */ import java.sql.SQLException;
/*    */ import java.util.ArrayList;
/*    */ import java.util.LinkedHashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import org.springframework.dao.DataAccessException;
/*    */ import org.springframework.jdbc.core.ResultSetExtractor;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AuthoritiesResultExtractor
/*    */   implements ResultSetExtractor<List<Authorities>>
/*    */ {
/*    */   public List<Authorities> extractData(ResultSet rs) throws SQLException, DataAccessException {
/* 20 */     Map<String, Authorities> authorities = new LinkedHashMap<>();
/* 21 */     while (rs.next()) {
/* 22 */       String username = rs.getString("authority");
/* 23 */       Authorities authority = authorities.get(username);
/* 24 */       if (authority == null) {
/* 25 */         authority = new Authorities(username, new ArrayList());
/*    */         
/* 27 */         authorities.put(username, authority);
/*    */       } 
/* 29 */       authority.users().add(new UserTest(rs.getString("username"), rs.getString("password"), Boolean.valueOf(rs.getBoolean("enabled")), rs.getString("test")));
/*    */     } 
/* 31 */     return new ArrayList<>(authorities.values());
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\mapper\AuthoritiesResultExtractor.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */