/*    */ package com.aaron.authserver.mapper;
/*    */ 
/*    */ import com.aaron.authserver.model.Authorities;
/*    */ import com.aaron.authserver.model.UserTest;
/*    */ import java.sql.ResultSet;
/*    */ import java.sql.SQLException;
/*    */ import java.util.ArrayList;
/*    */ import java.util.LinkedHashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import org.springframework.dao.DataAccessException;
/*    */ import org.springframework.jdbc.core.ResultSetExtractor;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AuthoritiesResultExtractor
/*    */   implements ResultSetExtractor<List<Authorities>>
/*    */ {
/*    */   public List<Authorities> extractData(ResultSet rs) throws SQLException, DataAccessException {
     Map<String, Authorities> authorities = new LinkedHashMap<>();
     while (rs.next()) {
       String username = rs.getString("authority");
       Authorities authority = authorities.get(username);
       if (authority == null) {
         authority = new Authorities(username, new ArrayList());
/*    */         
         authorities.put(username, authority);
/*    */       } 
       authority.users().add(new UserTest(rs.getString("username"), rs.getString("password"), Boolean.valueOf(rs.getBoolean("enabled")), rs.getString("test")));
/*    */     } 
     return new ArrayList<>(authorities.values());
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\mapper\AuthoritiesResultExtractor.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
