/*    */ package com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import java.security.MessageDigest;
/*    */ import org.springframework.security.crypto.codec.Utf8;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ final class PasswordEncoderUtils
/*    */ {
/*    */   static boolean equals(String expected, String actual) {
     byte[] expectedBytes = bytesUtf8(expected);
     byte[] actualBytes = bytesUtf8(actual);
     return MessageDigest.isEqual(expectedBytes, actualBytes);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   private static byte[] bytesUtf8(String s) {
     return (s != null) ? Utf8.encode(s) : null;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\PasswordEncoderUtils.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
