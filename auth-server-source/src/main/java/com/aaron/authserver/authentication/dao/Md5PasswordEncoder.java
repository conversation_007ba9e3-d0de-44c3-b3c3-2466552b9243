/*    */ package BOOT-INF.classes.com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import com.aaron.authserver.authentication.dao.AHMessageDigestPasswordEncoder;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Md5PasswordEncoder
/*    */   extends AHMessageDigestPasswordEncoder
/*    */ {
/*    */   public Md5PasswordEncoder() {
/* 12 */     super("MD5");
/*    */   }
/*    */   
/*    */   public Md5PasswordEncoder(String algorithm) {
/* 16 */     super(algorithm);
/*    */   }
/*    */   
/*    */   public Md5PasswordEncoder(String algorithm, boolean encodeHashAsBase64) throws IllegalArgumentException {
/* 20 */     super(algorithm, encodeHashAsBase64);
/*    */   }
/*    */ 
/*    */   
/*    */   public String encode(CharSequence rawPassword) {
/* 25 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean matches(CharSequence rawPassword, String encodedPassword) {
/* 30 */     return false;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\Md5PasswordEncoder.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */