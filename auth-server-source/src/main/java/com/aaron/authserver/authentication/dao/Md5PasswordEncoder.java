/*    */ package com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import com.aaron.authserver.authentication.dao.AHMessageDigestPasswordEncoder;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Md5PasswordEncoder
/*    */   extends AHMessageDigestPasswordEncoder
/*    */ {
/*    */   public Md5PasswordEncoder() {
     super("MD5");
/*    */   }
/*    */   
/*    */   public Md5PasswordEncoder(String algorithm) {
     super(algorithm);
/*    */   }
/*    */   
/*    */   public Md5PasswordEncoder(String algorithm, boolean encodeHashAsBase64) throws IllegalArgumentException {
     super(algorithm, encodeHashAsBase64);
/*    */   }
/*    */ 
/*    */   
/*    */   public String encode(CharSequence rawPassword) {
     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean matches(CharSequence rawPassword, String encodedPassword) {
     return false;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\Md5PasswordEncoder.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
