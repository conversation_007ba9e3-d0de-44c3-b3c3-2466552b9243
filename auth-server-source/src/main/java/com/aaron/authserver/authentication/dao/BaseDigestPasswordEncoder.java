package com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import com.aaron.authserver.authentication.dao.BasePasswordEncoder;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class BaseDigestPasswordEncoder
/*    */   extends BasePasswordEncoder
/*    */ {
/*    */   private boolean encodeHashAsBase64 = false;
/*    */   
/*    */   public boolean getEncodeHashAsBase64() {
     return this.encodeHashAsBase64;
/*    */   }
/*    */   
/*    */   public void setEncodeHashAsBase64(boolean encodeHashAsBase64) {
     this.encodeHashAsBase64 = encodeHashAsBase64;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\BaseDigestPasswordEncoder.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
