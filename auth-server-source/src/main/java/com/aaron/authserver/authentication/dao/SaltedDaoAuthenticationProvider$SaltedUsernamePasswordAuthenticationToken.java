/*    */ package com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import com.aaron.authserver.authentication.dao.SaltedDaoAuthenticationProvider;
/*    */ import com.aaron.authserver.model.IUser;
/*    */ import java.util.Collection;
/*    */ import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
/*    */ import org.springframework.security.core.GrantedAuthority;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class SaltedUsernamePasswordAuthenticationToken
/*    */   extends UsernamePasswordAuthenticationToken
/*    */ {
/*    */   private IUser subject;
/*    */   
/*    */   SaltedUsernamePasswordAuthenticationToken(Object principal, Object credentials, Collection<? extends GrantedAuthority> authorities, IUser subject) {
     super(principal, credentials, authorities);
     this.subject = subject;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Object getCredentials() {
     return super.getCredentials();
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\SaltedDaoAuthenticationProvider$SaltedUsernamePasswordAuthenticationToken.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
