/*    */ package com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import com.aaron.authserver.authentication.dao.BaseDigestPasswordEncoder;
/*    */ import com.aaron.authserver.authentication.dao.PasswordEncoderUtils;
/*    */ import java.security.MessageDigest;
/*    */ import java.security.NoSuchAlgorithmException;
/*    */ import org.springframework.security.crypto.codec.Base64;
/*    */ import org.springframework.security.crypto.codec.Hex;
/*    */ import org.springframework.security.crypto.codec.Utf8;
/*    */ import org.springframework.util.Assert;
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class AHMessageDigestPasswordEncoder
/*    */   extends BaseDigestPasswordEncoder
/*    */ {
/*    */   private final String algorithm;
/*    */   private int iterations;
/*    */   
/*    */   public AHMessageDigestPasswordEncoder(String algorithm) {
     this(algorithm, false);
/*    */   }
/*    */   
/*    */   public AHMessageDigestPasswordEncoder(String algorithm, boolean encodeHashAsBase64) throws IllegalArgumentException {
     this.iterations = 1;
     this.algorithm = algorithm;
     setEncodeHashAsBase64(encodeHashAsBase64);
     getMessageDigest();
/*    */   }
/*    */   
/*    */   public String encodePassword(String rawPass, Object salt) {
     String saltedPass = mergePasswordAndSalt(rawPass, salt, false);
     MessageDigest messageDigest = getMessageDigest();
     byte[] digest = messageDigest.digest(Utf8.encode(saltedPass));
/*    */     
     for (int i = 1; i < this.iterations; i++) {
       digest = messageDigest.digest(digest);
/*    */     }
/*    */     
     return getEncodeHashAsBase64() ? Utf8.decode(Base64.encode(digest)) : new String(Hex.encode(digest));
/*    */   }
/*    */   
/*    */   protected final MessageDigest getMessageDigest() throws IllegalArgumentException {
/*    */     try {
       return MessageDigest.getInstance(this.algorithm);
     } catch (NoSuchAlgorithmException var2) {
       throw new IllegalArgumentException("No such algorithm [" + this.algorithm + "]");
/*    */     } 
/*    */   }
/*    */   
/*    */   public boolean isPasswordValid(String encPass, String rawPass, Object salt) {
     String pass2 = encodePassword(rawPass, salt);
     return PasswordEncoderUtils.equals(encPass, pass2);
/*    */   }
/*    */   
/*    */   public String getAlgorithm() {
     return this.algorithm;
/*    */   }
/*    */   
/*    */   public void setIterations(int iterations) {
     Assert.isTrue((iterations > 0), "Iterations value must be greater than zero");
     this.iterations = iterations;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\AHMessageDigestPasswordEncoder.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
