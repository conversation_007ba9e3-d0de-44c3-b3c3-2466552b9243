/*    */ package BOOT-INF.classes.com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import com.aaron.authserver.authentication.dao.BaseDigestPasswordEncoder;
/*    */ import com.aaron.authserver.authentication.dao.PasswordEncoderUtils;
/*    */ import java.security.MessageDigest;
/*    */ import java.security.NoSuchAlgorithmException;
/*    */ import org.springframework.security.crypto.codec.Base64;
/*    */ import org.springframework.security.crypto.codec.Hex;
/*    */ import org.springframework.security.crypto.codec.Utf8;
/*    */ import org.springframework.util.Assert;
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class AHMessageDigestPasswordEncoder
/*    */   extends BaseDigestPasswordEncoder
/*    */ {
/*    */   private final String algorithm;
/*    */   private int iterations;
/*    */   
/*    */   public AHMessageDigestPasswordEncoder(String algorithm) {
/* 21 */     this(algorithm, false);
/*    */   }
/*    */   
/*    */   public AHMessageDigestPasswordEncoder(String algorithm, boolean encodeHashAsBase64) throws IllegalArgumentException {
/* 25 */     this.iterations = 1;
/* 26 */     this.algorithm = algorithm;
/* 27 */     setEncodeHashAsBase64(encodeHashAsBase64);
/* 28 */     getMessageDigest();
/*    */   }
/*    */   
/*    */   public String encodePassword(String rawPass, Object salt) {
/* 32 */     String saltedPass = mergePasswordAndSalt(rawPass, salt, false);
/* 33 */     MessageDigest messageDigest = getMessageDigest();
/* 34 */     byte[] digest = messageDigest.digest(Utf8.encode(saltedPass));
/*    */     
/* 36 */     for (int i = 1; i < this.iterations; i++) {
/* 37 */       digest = messageDigest.digest(digest);
/*    */     }
/*    */     
/* 40 */     return getEncodeHashAsBase64() ? Utf8.decode(Base64.encode(digest)) : new String(Hex.encode(digest));
/*    */   }
/*    */   
/*    */   protected final MessageDigest getMessageDigest() throws IllegalArgumentException {
/*    */     try {
/* 45 */       return MessageDigest.getInstance(this.algorithm);
/* 46 */     } catch (NoSuchAlgorithmException var2) {
/* 47 */       throw new IllegalArgumentException("No such algorithm [" + this.algorithm + "]");
/*    */     } 
/*    */   }
/*    */   
/*    */   public boolean isPasswordValid(String encPass, String rawPass, Object salt) {
/* 52 */     String pass2 = encodePassword(rawPass, salt);
/* 53 */     return PasswordEncoderUtils.equals(encPass, pass2);
/*    */   }
/*    */   
/*    */   public String getAlgorithm() {
/* 57 */     return this.algorithm;
/*    */   }
/*    */   
/*    */   public void setIterations(int iterations) {
/* 61 */     Assert.isTrue((iterations > 0), "Iterations value must be greater than zero");
/* 62 */     this.iterations = iterations;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\AHMessageDigestPasswordEncoder.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */