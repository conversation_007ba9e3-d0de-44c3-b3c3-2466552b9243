package com.aaron.authserver.authentication.dao;
/*     */ 
/*     */ import com.aaron.authserver.authentication.dao.SaltPasswordEncoder;
/*     */ import com.aaron.authserver.authentication.dao.SaltSource;
/*     */ import com.aaron.authserver.model.IUser;
/*     */ import org.springframework.security.authentication.BadCredentialsException;
/*     */ import org.springframework.security.authentication.InternalAuthenticationServiceException;
/*     */ import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
/*     */ import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
/*     */ import org.springframework.security.core.Authentication;
/*     */ import org.springframework.security.core.AuthenticationException;
/*     */ import org.springframework.security.core.GrantedAuthority;
/*     */ import org.springframework.security.core.userdetails.UserDetails;
/*     */ import java.util.Collection;
/*     */ import org.springframework.security.core.userdetails.UserDetailsPasswordService;
/*     */ import org.springframework.security.core.userdetails.UserDetailsService;
/*     */ import org.springframework.security.core.userdetails.UsernameNotFoundException;
/*     */ import org.springframework.security.crypto.password.PasswordEncoder;
/*     */ import org.springframework.util.Assert;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SaltedDaoAuthenticationProvider
/*     */   extends AbstractUserDetailsAuthenticationProvider
/*     */ {
/*     */   private SaltSource saltSource;
/*     */   private static final String USER_NOT_FOUND_PASSWORD = "userNotFoundPassword";
/*     */   private PasswordEncoder passwordEncoder;
/*     */   private volatile String userNotFoundEncodedPassword;
/*     */   private UserDetailsService userDetailsService;
/*     */   private UserDetailsPasswordService userDetailsPasswordService;
/*     */   
/*     */   protected void additionalAuthenticationChecks(UserDetails details, UsernamePasswordAuthenticationToken auth) throws AuthenticationException {
/*     */     SaltedUsernamePasswordAuthenticationToken saltedUsernamePasswordAuthenticationToken = null;
     if (details instanceof IUser)
/*     */     {
/*     */
       saltedUsernamePasswordAuthenticationToken = new SaltedUsernamePasswordAuthenticationToken(auth.getPrincipal(), auth.getCredentials(), auth.getAuthorities(), (IUser)details);
/*     */     } else {
       saltedUsernamePasswordAuthenticationToken = new SaltedUsernamePasswordAuthenticationToken(auth.getPrincipal(), auth.getCredentials(), auth.getAuthorities(), null);
     }
/*     */ 
/*     */ 
/*     */     
     Object salt = null;
/*     */     
     if (this.saltSource != null) {
       salt = this.saltSource.getSalt(details);
/*     */     }
/*     */     
     if (saltedUsernamePasswordAuthenticationToken.getCredentials() == null) {
       this.logger.debug("Failed to authenticate since no credentials provided");
       throw new BadCredentialsException(this.messages
           .getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
/*     */     } 
     String presentedPassword = saltedUsernamePasswordAuthenticationToken.getCredentials().toString();
     if (getPasswordEncoder() instanceof SaltPasswordEncoder && 
       !((SaltPasswordEncoder)getPasswordEncoder()).isPasswordValid(details.getPassword(), presentedPassword, salt)) {
       this.logger.debug("Failed to authenticate since password does not match stored value");
       throw new BadCredentialsException(this.messages
           .getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void doAfterPropertiesSet() {
     Assert.notNull(this.userDetailsService, "A UserDetailsService must be set");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   protected final UserDetails retrieveUser(String username, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
     prepareTimingAttackProtection();
/*     */     try {
       UserDetails loadedUser = getUserDetailsService().loadUserByUsername(username);
       if (loadedUser == null) {
         throw new InternalAuthenticationServiceException("UserDetailsService returned null, which is an interface contract violation");
/*     */       }
/*     */       
       return loadedUser;
/*     */     }
     catch (UsernameNotFoundException ex) {
       mitigateAgainstTimingAttack(authentication);
       throw ex;
/*     */     }
     catch (InternalAuthenticationServiceException ex) {
       throw ex;
/*     */     }
     catch (Exception ex) {
       throw new InternalAuthenticationServiceException(ex.getMessage(), ex);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected Authentication createSuccessAuthentication(Object principal, Authentication authentication, UserDetails user) {
     boolean upgradeEncoding = (this.userDetailsPasswordService != null && this.passwordEncoder.upgradeEncoding(user.getPassword()));
     if (upgradeEncoding) {
       String presentedPassword = authentication.getCredentials().toString();
       String newPassword = this.passwordEncoder.encode(presentedPassword);
       user = this.userDetailsPasswordService.updatePassword(user, newPassword);
/*     */     } 
     return super.createSuccessAuthentication(principal, authentication, user);
/*     */   }
/*     */   
/*     */   private void prepareTimingAttackProtection() {
     if (this.userNotFoundEncodedPassword == null) {
       this.userNotFoundEncodedPassword = this.passwordEncoder.encode("userNotFoundPassword");
/*     */     }
/*     */   }
/*     */   
/*     */   private void mitigateAgainstTimingAttack(UsernamePasswordAuthenticationToken authentication) {
     if (authentication.getCredentials() != null) {
       String presentedPassword = authentication.getCredentials().toString();
       this.passwordEncoder.matches(presentedPassword, this.userNotFoundEncodedPassword);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPasswordEncoder(PasswordEncoder passwordEncoder) {
     Assert.notNull(passwordEncoder, "passwordEncoder cannot be null");
     this.passwordEncoder = passwordEncoder;
     this.userNotFoundEncodedPassword = null;
/*     */   }
/*     */   
/*     */   protected PasswordEncoder getPasswordEncoder() {
     return this.passwordEncoder;
/*     */   }
/*     */   
/*     */   public void setUserDetailsService(UserDetailsService userDetailsService) {
     this.userDetailsService = userDetailsService;
/*     */   }
/*     */   
/*     */   protected UserDetailsService getUserDetailsService() {
     return this.userDetailsService;
/*     */   }
/*     */   
/*     */   public void setUserDetailsPasswordService(UserDetailsPasswordService userDetailsPasswordService) {
     this.userDetailsPasswordService = userDetailsPasswordService;
/*     */   }
/*     */   
/*     */   public SaltSource getSaltSource() {
     return this.saltSource;
/*     */   }
/*     */   
/*     */   public void setSaltSource(SaltSource saltSource) {
     this.saltSource = saltSource;
/*     */   }

   // Inner class SaltedUsernamePasswordAuthenticationToken
   public static class SaltedUsernamePasswordAuthenticationToken extends UsernamePasswordAuthenticationToken {
       private final IUser user;

       public SaltedUsernamePasswordAuthenticationToken(Object principal, Object credentials,
                                                       Collection<? extends GrantedAuthority> authorities, IUser user) {
           super(principal, credentials, authorities);
           this.user = user;
       }

       public IUser getUser() {
           return user;
       }
   }
/*     */ }
