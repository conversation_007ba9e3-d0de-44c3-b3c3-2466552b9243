/*     */ package BOOT-INF.classes.com.aaron.authserver.authentication.dao;
/*     */ 
/*     */ import com.aaron.authserver.authentication.dao.SaltPasswordEncoder;
/*     */ import com.aaron.authserver.authentication.dao.SaltSource;
/*     */ import com.aaron.authserver.model.IUser;
/*     */ import org.springframework.security.authentication.BadCredentialsException;
/*     */ import org.springframework.security.authentication.InternalAuthenticationServiceException;
/*     */ import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
/*     */ import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
/*     */ import org.springframework.security.core.Authentication;
/*     */ import org.springframework.security.core.AuthenticationException;
/*     */ import org.springframework.security.core.userdetails.UserDetails;
/*     */ import org.springframework.security.core.userdetails.UserDetailsPasswordService;
/*     */ import org.springframework.security.core.userdetails.UserDetailsService;
/*     */ import org.springframework.security.core.userdetails.UsernameNotFoundException;
/*     */ import org.springframework.security.crypto.password.PasswordEncoder;
/*     */ import org.springframework.util.Assert;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SaltedDaoAuthenticationProvider
/*     */   extends AbstractUserDetailsAuthenticationProvider
/*     */ {
/*     */   private SaltSource saltSource;
/*     */   private static final String USER_NOT_FOUND_PASSWORD = "userNotFoundPassword";
/*     */   private PasswordEncoder passwordEncoder;
/*     */   private volatile String userNotFoundEncodedPassword;
/*     */   private UserDetailsService userDetailsService;
/*     */   private UserDetailsPasswordService userDetailsPasswordService;
/*     */   
/*     */   protected void additionalAuthenticationChecks(UserDetails details, UsernamePasswordAuthenticationToken auth) throws AuthenticationException {
/*     */     SaltedUsernamePasswordAuthenticationToken saltedUsernamePasswordAuthenticationToken;
/*  71 */     if (details instanceof IUser)
/*     */     {
/*     */       
/*  74 */       saltedUsernamePasswordAuthenticationToken = new SaltedUsernamePasswordAuthenticationToken(auth.getPrincipal(), auth.getCredentials(), auth.getAuthorities(), (IUser)details);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*  79 */     Object salt = null;
/*     */     
/*  81 */     if (this.saltSource != null) {
/*  82 */       salt = this.saltSource.getSalt(details);
/*     */     }
/*     */     
/*  85 */     if (saltedUsernamePasswordAuthenticationToken.getCredentials() == null) {
/*  86 */       this.logger.debug("Failed to authenticate since no credentials provided");
/*  87 */       throw new BadCredentialsException(this.messages
/*  88 */           .getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
/*     */     } 
/*  90 */     String presentedPassword = saltedUsernamePasswordAuthenticationToken.getCredentials().toString();
/*  91 */     if (getPasswordEncoder() instanceof SaltPasswordEncoder && 
/*  92 */       !((SaltPasswordEncoder)getPasswordEncoder()).isPasswordValid(details.getPassword(), presentedPassword, salt)) {
/*  93 */       this.logger.debug("Failed to authenticate since password does not match stored value");
/*  94 */       throw new BadCredentialsException(this.messages
/*  95 */           .getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void doAfterPropertiesSet() {
/* 103 */     Assert.notNull(this.userDetailsService, "A UserDetailsService must be set");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   protected final UserDetails retrieveUser(String username, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
/* 109 */     prepareTimingAttackProtection();
/*     */     try {
/* 111 */       UserDetails loadedUser = getUserDetailsService().loadUserByUsername(username);
/* 112 */       if (loadedUser == null) {
/* 113 */         throw new InternalAuthenticationServiceException("UserDetailsService returned null, which is an interface contract violation");
/*     */       }
/*     */       
/* 116 */       return loadedUser;
/*     */     }
/* 118 */     catch (UsernameNotFoundException ex) {
/* 119 */       mitigateAgainstTimingAttack(authentication);
/* 120 */       throw ex;
/*     */     }
/* 122 */     catch (InternalAuthenticationServiceException ex) {
/* 123 */       throw ex;
/*     */     }
/* 125 */     catch (Exception ex) {
/* 126 */       throw new InternalAuthenticationServiceException(ex.getMessage(), ex);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected Authentication createSuccessAuthentication(Object principal, Authentication authentication, UserDetails user) {
/* 134 */     boolean upgradeEncoding = (this.userDetailsPasswordService != null && this.passwordEncoder.upgradeEncoding(user.getPassword()));
/* 135 */     if (upgradeEncoding) {
/* 136 */       String presentedPassword = authentication.getCredentials().toString();
/* 137 */       String newPassword = this.passwordEncoder.encode(presentedPassword);
/* 138 */       user = this.userDetailsPasswordService.updatePassword(user, newPassword);
/*     */     } 
/* 140 */     return super.createSuccessAuthentication(principal, authentication, user);
/*     */   }
/*     */   
/*     */   private void prepareTimingAttackProtection() {
/* 144 */     if (this.userNotFoundEncodedPassword == null) {
/* 145 */       this.userNotFoundEncodedPassword = this.passwordEncoder.encode("userNotFoundPassword");
/*     */     }
/*     */   }
/*     */   
/*     */   private void mitigateAgainstTimingAttack(UsernamePasswordAuthenticationToken authentication) {
/* 150 */     if (authentication.getCredentials() != null) {
/* 151 */       String presentedPassword = authentication.getCredentials().toString();
/* 152 */       this.passwordEncoder.matches(presentedPassword, this.userNotFoundEncodedPassword);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setPasswordEncoder(PasswordEncoder passwordEncoder) {
/* 164 */     Assert.notNull(passwordEncoder, "passwordEncoder cannot be null");
/* 165 */     this.passwordEncoder = passwordEncoder;
/* 166 */     this.userNotFoundEncodedPassword = null;
/*     */   }
/*     */   
/*     */   protected PasswordEncoder getPasswordEncoder() {
/* 170 */     return this.passwordEncoder;
/*     */   }
/*     */   
/*     */   public void setUserDetailsService(UserDetailsService userDetailsService) {
/* 174 */     this.userDetailsService = userDetailsService;
/*     */   }
/*     */   
/*     */   protected UserDetailsService getUserDetailsService() {
/* 178 */     return this.userDetailsService;
/*     */   }
/*     */   
/*     */   public void setUserDetailsPasswordService(UserDetailsPasswordService userDetailsPasswordService) {
/* 182 */     this.userDetailsPasswordService = userDetailsPasswordService;
/*     */   }
/*     */   
/*     */   public SaltSource getSaltSource() {
/* 186 */     return this.saltSource;
/*     */   }
/*     */   
/*     */   public void setSaltSource(SaltSource saltSource) {
/* 190 */     this.saltSource = saltSource;
/*     */   }
/*     */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\SaltedDaoAuthenticationProvider.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */