package com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import org.springframework.security.crypto.password.PasswordEncoder;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public interface SaltPasswordEncoder
/*    */   extends PasswordEncoder
/*    */ {
/*    */   String encode(CharSequence paramCharSequence);
/*    */   
/*    */   boolean matches(CharSequence paramCharSequence, String paramString);
/*    */   
/*    */   default boolean upgradeEncoding(String encodedPassword) {
     return false;
/*    */   }
/*    */   
/*    */   String encodePassword(String paramString, Object paramObject);
/*    */   
/*    */   boolean isPasswordValid(String paramString1, String paramString2, Object paramObject);
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\SaltPasswordEncoder.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
