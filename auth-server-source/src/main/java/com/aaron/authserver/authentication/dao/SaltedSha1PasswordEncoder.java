/*    */ package BOOT-INF.classes.com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import com.aaron.authserver.authentication.dao.AHMessageDigestPasswordEncoder;
/*    */ import com.aaron.authserver.authentication.dao.Md5PasswordEncoder;
/*    */ import org.slf4j.Logger;
/*    */ import org.slf4j.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SaltedSha1PasswordEncoder
/*    */   extends AHMessageDigestPasswordEncoder
/*    */ {
/* 17 */   private final Logger logger = LoggerFactory.getLogger(com.aaron.authserver.authentication.dao.SaltedSha1PasswordEncoder.class);
/*    */   
/*    */   private static final String SALT = "wwwEnyanE";
/*    */   
/*    */   public SaltedSha1PasswordEncoder() {
/* 22 */     super("SHA1");
/*    */   }
/*    */ 
/*    */   
/*    */   public String encodePassword(String rawPass, Object salt) {
/* 27 */     String value = super.encodePassword(rawPass, "");
/*    */ 
/*    */     
/* 30 */     value = super.encodePassword("" + salt + salt, "");
/*    */ 
/*    */     
/* 33 */     value = super.encodePassword("" + salt + salt, "");
/*    */ 
/*    */     
/* 36 */     return value;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean isPasswordValid(String encPass, String rawPass, Object salt) {
/* 42 */     String pass1 = encPass;
/* 43 */     String pass2 = encodePassword(rawPass, salt);
/*    */     
/* 45 */     return pass1.equals(pass2);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String encode(CharSequence rawPassword) {
/* 52 */     String value = super.encodePassword(rawPassword.toString(), "");
/*    */ 
/*    */     
/* 55 */     value = super.encodePassword("wwwEnyanE" + value, "");
/*    */ 
/*    */     
/* 58 */     value = super.encodePassword("wwwEnyanE" + value, "");
/*    */ 
/*    */     
/* 61 */     return value;
/*    */   }
/*    */   
/*    */   public boolean matches(CharSequence rawPassword, String encodedPassword) {
/* 65 */     String tmpSalt = "wwwEnyanE";
/* 66 */     String rawPass = rawPassword.toString();
/* 67 */     String[] strs = rawPassword.toString().split(",");
/* 68 */     if (strs.length == 2) {
/* 69 */       rawPass = strs[0];
/* 70 */       tmpSalt = strs[1];
/*    */     } 
/*    */     
/* 73 */     return isPasswordValid(encodedPassword, rawPass, tmpSalt);
/*    */   }
/*    */   
/*    */   public static void main(String[] args) {
/* 77 */     Md5PasswordEncoder md5PasswordEncoder = new Md5PasswordEncoder();
/* 78 */     System.out.println(md5PasswordEncoder.encode("123456"));
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\SaltedSha1PasswordEncoder.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */