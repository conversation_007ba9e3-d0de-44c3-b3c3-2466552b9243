/*    */ package com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import com.aaron.authserver.authentication.dao.AHMessageDigestPasswordEncoder;
/*    */ import com.aaron.authserver.authentication.dao.Md5PasswordEncoder;
/*    */ import org.slf4j.Logger;
/*    */ import org.slf4j.LoggerFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SaltedSha1PasswordEncoder
/*    */   extends AHMessageDigestPasswordEncoder
/*    */ {
   private final Logger logger = LoggerFactory.getLogger(com.aaron.authserver.authentication.dao.SaltedSha1PasswordEncoder.class);
/*    */   
/*    */   private static final String SALT = "wwwEnyanE";
/*    */   
/*    */   public SaltedSha1PasswordEncoder() {
     super("SHA1");
/*    */   }
/*    */ 
/*    */   
/*    */   public String encodePassword(String rawPass, Object salt) {
     String value = super.encodePassword(rawPass, "");
/*    */ 
/*    */     
     value = super.encodePassword("" + salt + salt, "");
/*    */ 
/*    */     
     value = super.encodePassword("" + salt + salt, "");
/*    */ 
/*    */     
     return value;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean isPasswordValid(String encPass, String rawPass, Object salt) {
     String pass1 = encPass;
     String pass2 = encodePassword(rawPass, salt);
/*    */     
     return pass1.equals(pass2);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String encode(CharSequence rawPassword) {
     String value = super.encodePassword(rawPassword.toString(), "");
/*    */ 
/*    */     
     value = super.encodePassword("wwwEnyanE" + value, "");
/*    */ 
/*    */     
     value = super.encodePassword("wwwEnyanE" + value, "");
/*    */ 
/*    */     
     return value;
/*    */   }
/*    */   
/*    */   public boolean matches(CharSequence rawPassword, String encodedPassword) {
     String tmpSalt = "wwwEnyanE";
     String rawPass = rawPassword.toString();
     String[] strs = rawPassword.toString().split(",");
     if (strs.length == 2) {
       rawPass = strs[0];
       tmpSalt = strs[1];
/*    */     } 
/*    */     
     return isPasswordValid(encodedPassword, rawPass, tmpSalt);
/*    */   }
/*    */   
/*    */   public static void main(String[] args) {
     Md5PasswordEncoder md5PasswordEncoder = new Md5PasswordEncoder();
     System.out.println(md5PasswordEncoder.encode("123456"));
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\SaltedSha1PasswordEncoder.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
