package com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import com.aaron.authserver.authentication.dao.SaltPasswordEncoder;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class BasePasswordEncoder
/*    */   implements SaltPasswordEncoder
/*    */ {
/*    */   protected String[] demergePasswordAndSalt(String mergedPasswordSalt) {
     if (mergedPasswordSalt != null && !"".equals(mergedPasswordSalt)) {
       String password = mergedPasswordSalt;
       String salt = "";
       int saltBegins = mergedPasswordSalt.lastIndexOf("{");
       if (saltBegins != -1 && saltBegins + 1 < mergedPasswordSalt.length()) {
         salt = mergedPasswordSalt.substring(saltBegins + 1, mergedPasswordSalt.length() - 1);
         password = mergedPasswordSalt.substring(0, saltBegins);
/*    */       } 
/*    */       
       return new String[] { password, salt };
/*    */     } 
     throw new IllegalArgumentException("Cannot pass a null or empty String");
/*    */   }
/*    */ 
/*    */   
/*    */   protected String mergePasswordAndSalt(String password, Object salt, boolean strict) {
     if (password == null) {
       password = "";
/*    */     }
/*    */     
     if (strict && salt != null && (salt.toString().lastIndexOf("{") != -1 || salt.toString().lastIndexOf("}") != -1)) {
       throw new IllegalArgumentException("Cannot use { or } in salt.toString()");
/*    */     }
     return (salt != null && !"".equals(salt)) ? (password + "{" + password + "}") : password;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\BasePasswordEncoder.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
