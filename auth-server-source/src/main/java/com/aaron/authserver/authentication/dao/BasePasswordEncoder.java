/*    */ package BOOT-INF.classes.com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import com.aaron.authserver.authentication.dao.SaltPasswordEncoder;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class BasePasswordEncoder
/*    */   implements SaltPasswordEncoder
/*    */ {
/*    */   protected String[] demergePasswordAndSalt(String mergedPasswordSalt) {
/* 14 */     if (mergedPasswordSalt != null && !"".equals(mergedPasswordSalt)) {
/* 15 */       String password = mergedPasswordSalt;
/* 16 */       String salt = "";
/* 17 */       int saltBegins = mergedPasswordSalt.lastIndexOf("{");
/* 18 */       if (saltBegins != -1 && saltBegins + 1 < mergedPasswordSalt.length()) {
/* 19 */         salt = mergedPasswordSalt.substring(saltBegins + 1, mergedPasswordSalt.length() - 1);
/* 20 */         password = mergedPasswordSalt.substring(0, saltBegins);
/*    */       } 
/*    */       
/* 23 */       return new String[] { password, salt };
/*    */     } 
/* 25 */     throw new IllegalArgumentException("Cannot pass a null or empty String");
/*    */   }
/*    */ 
/*    */   
/*    */   protected String mergePasswordAndSalt(String password, Object salt, boolean strict) {
/* 30 */     if (password == null) {
/* 31 */       password = "";
/*    */     }
/*    */     
/* 34 */     if (strict && salt != null && (salt.toString().lastIndexOf("{") != -1 || salt.toString().lastIndexOf("}") != -1)) {
/* 35 */       throw new IllegalArgumentException("Cannot use { or } in salt.toString()");
/*    */     }
/* 37 */     return (salt != null && !"".equals(salt)) ? (password + "{" + password + "}") : password;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\BasePasswordEncoder.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */