package com.aaron.authserver.authentication.dao;
/*    */ 
/*    */ import com.aaron.authserver.authentication.dao.SaltSource;
/*    */ import com.aaron.authserver.model.IUser;
/*    */ import org.springframework.security.core.userdetails.UserDetails;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class PasswordSaltSource
/*    */   implements SaltSource
/*    */ {
/*    */   public Object getSalt(UserDetails user) {
     if (user instanceof IUser) {
       return ((IUser)user).getSalt();
/*    */     }
     return null;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\authentication\dao\PasswordSaltSource.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
