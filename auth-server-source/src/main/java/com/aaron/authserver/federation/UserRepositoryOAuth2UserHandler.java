/*    */ package BOOT-INF.classes.com.aaron.authserver.federation;
/*    */ 
/*    */ import java.util.function.Consumer;
/*    */ import org.springframework.security.oauth2.core.user.OAuth2User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class UserRepositoryOAuth2UserHandler
/*    */   implements Consumer<OAuth2User>
/*    */ {
/* 35 */   private final UserRepository userRepository = new UserRepository();
/*    */ 
/*    */ 
/*    */   
/*    */   public void accept(OAuth2User user) {
/* 40 */     if (this.userRepository.findByName(user.getName()) == null) {
/* 41 */       System.out.println("Saving first-time user: name=" + user.getName() + ", claims=" + user.getAttributes() + ", authorities=" + user.getAuthorities());
/* 42 */       this.userRepository.save(user);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\federation\UserRepositoryOAuth2UserHandler.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */