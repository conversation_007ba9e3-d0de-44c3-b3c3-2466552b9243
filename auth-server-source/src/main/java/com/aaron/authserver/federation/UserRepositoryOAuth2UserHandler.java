package com.aaron.authserver.federation;

import java.util.function.Consumer;
import org.springframework.security.oauth2.core.user.OAuth2User;

public final class UserRepositoryOAuth2UserHandler implements Consumer<OAuth2User> {

    private final UserRepository userRepository = new UserRepository();

    public void accept(OAuth2User user) {
        if (this.userRepository.findByName(user.getName()) == null) {
            System.out.println("Saving first-time user: name=" + user.getName() +
                             ", claims=" + user.getAttributes() +
                             ", authorities=" + user.getAuthorities());
            this.userRepository.save(user);
        }
    }

    // Inner class UserRepository
    public static class UserRepository {

        public OAuth2User findByName(String name) {
            // TODO: Implement actual database lookup
            return null;
        }

        public void save(OAuth2User user) {
            // TODO: Implement actual database save
            System.out.println("User saved: " + user.getName());
        }
    }
}
