/*    */ package BOOT-INF.classes.com.aaron.authserver.federation;
/*    */ 
/*    */ import java.util.Arrays;
/*    */ import java.util.Collections;
/*    */ import java.util.HashMap;
/*    */ import java.util.HashSet;
/*    */ import java.util.Map;
/*    */ import java.util.Objects;
/*    */ import java.util.Set;
/*    */ import org.springframework.security.core.Authentication;
/*    */ import org.springframework.security.oauth2.core.oidc.OidcIdToken;
/*    */ import org.springframework.security.oauth2.core.oidc.user.OidcUser;
/*    */ import org.springframework.security.oauth2.core.user.OAuth2User;
/*    */ import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
/*    */ import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenContext;
/*    */ import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class FederatedIdentityIdTokenCustomizer
/*    */   implements OAuth2TokenCustomizer<JwtEncodingContext>
/*    */ {
/* 46 */   private static final Set<String> ID_TOKEN_CLAIMS = Collections.unmodifiableSet(new HashSet<>(Arrays.asList(new String[] { "iss", "sub", "aud", "exp", "iat", "auth_time", "nonce", "acr", "amr", "azp", "at_hash", "c_hash" })));
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void customize(JwtEncodingContext context) {
/* 63 */     if ("id_token".equals(context.getTokenType().getValue())) {
/* 64 */       Map<String, Object> thirdPartyClaims = extractClaims(context.getPrincipal());
/* 65 */       context.getClaims().claims(existingClaims -> {
/*    */             Objects.requireNonNull(thirdPartyClaims);
/*    */             existingClaims.keySet().forEach(thirdPartyClaims::remove);
/*    */             Objects.requireNonNull(thirdPartyClaims);
/*    */             ID_TOKEN_CLAIMS.forEach(thirdPartyClaims::remove);
/*    */             existingClaims.putAll(thirdPartyClaims);
/*    */           });
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private Map<String, Object> extractClaims(Authentication principal) {
/*    */     Map<String, Object> claims;
/* 80 */     if (principal.getPrincipal() instanceof OidcUser) {
/* 81 */       OidcUser oidcUser = (OidcUser)principal.getPrincipal();
/* 82 */       OidcIdToken idToken = oidcUser.getIdToken();
/* 83 */       claims = idToken.getClaims();
/* 84 */     } else if (principal.getPrincipal() instanceof OAuth2User) {
/* 85 */       OAuth2User oauth2User = (OAuth2User)principal.getPrincipal();
/* 86 */       claims = oauth2User.getAttributes();
/*    */     } else {
/* 88 */       claims = Collections.emptyMap();
/*    */     } 
/*    */     
/* 91 */     return new HashMap<>(claims);
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\federation\FederatedIdentityIdTokenCustomizer.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */