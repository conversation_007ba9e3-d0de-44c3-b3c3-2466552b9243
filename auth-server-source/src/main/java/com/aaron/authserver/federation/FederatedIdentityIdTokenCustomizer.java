/*    */ package com.aaron.authserver.federation;
/*    */ 
/*    */ import java.util.Arrays;
/*    */ import java.util.Collections;
/*    */ import java.util.HashMap;
/*    */ import java.util.HashSet;
/*    */ import java.util.Map;
/*    */ import java.util.Objects;
/*    */ import java.util.Set;
/*    */ import org.springframework.security.core.Authentication;
/*    */ import org.springframework.security.oauth2.core.oidc.OidcIdToken;
/*    */ import org.springframework.security.oauth2.core.oidc.user.OidcUser;
/*    */ import org.springframework.security.oauth2.core.user.OAuth2User;
/*    */ import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
/*    */ import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenContext;
/*    */ import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class FederatedIdentityIdTokenCustomizer
/*    */   implements OAuth2TokenCustomizer<JwtEncodingContext>
/*    */ {
   private static final Set<String> ID_TOKEN_CLAIMS = Collections.unmodifiableSet(new HashSet<>(Arrays.asList(new String[] { "iss", "sub", "aud", "exp", "iat", "auth_time", "nonce", "acr", "amr", "azp", "at_hash", "c_hash" })));
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void customize(JwtEncodingContext context) {
     if ("id_token".equals(context.getTokenType().getValue())) {
       Map<String, Object> thirdPartyClaims = extractClaims(context.getPrincipal());
       context.getClaims().claims(existingClaims -> {
/*    */             Objects.requireNonNull(thirdPartyClaims);
/*    */             existingClaims.keySet().forEach(thirdPartyClaims::remove);
/*    */             Objects.requireNonNull(thirdPartyClaims);
/*    */             ID_TOKEN_CLAIMS.forEach(thirdPartyClaims::remove);
/*    */             existingClaims.putAll(thirdPartyClaims);
/*    */           });
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private Map<String, Object> extractClaims(Authentication principal) {
/*    */     Map<String, Object> claims;
     if (principal.getPrincipal() instanceof OidcUser) {
       OidcUser oidcUser = (OidcUser)principal.getPrincipal();
       OidcIdToken idToken = oidcUser.getIdToken();
       claims = idToken.getClaims();
     } else if (principal.getPrincipal() instanceof OAuth2User) {
       OAuth2User oauth2User = (OAuth2User)principal.getPrincipal();
       claims = oauth2User.getAttributes();
/*    */     } else {
       claims = Collections.emptyMap();
/*    */     } 
/*    */     
     return new HashMap<>(claims);
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\federation\FederatedIdentityIdTokenCustomizer.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
