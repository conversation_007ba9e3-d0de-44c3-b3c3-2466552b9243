/*    */ package BOOT-INF.classes.com.aaron.authserver.federation;
/*    */ 
/*    */ import jakarta.servlet.ServletException;
/*    */ import jakarta.servlet.http.HttpServletRequest;
/*    */ import jakarta.servlet.http.HttpServletResponse;
/*    */ import java.io.IOException;
/*    */ import java.util.function.Consumer;
/*    */ import org.springframework.security.core.Authentication;
/*    */ import org.springframework.security.oauth2.core.oidc.user.OidcUser;
/*    */ import org.springframework.security.oauth2.core.user.OAuth2User;
/*    */ import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
/*    */ import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class FederatedIdentityAuthenticationSuccessHandler
/*    */   implements AuthenticationSuccessHandler
/*    */ {
/*    */   private final AuthenticationSuccessHandler delegate;
/*    */   private Consumer<OAuth2User> oauth2UserHandler;
/*    */   private Consumer<OidcUser> oidcUserHandler;
/*    */   
/*    */   public FederatedIdentityAuthenticationSuccessHandler() {
/* 44 */     this.delegate = (AuthenticationSuccessHandler)new SavedRequestAwareAuthenticationSuccessHandler();
/*    */     
/* 46 */     this.oauth2UserHandler = (user -> {
/*    */       
/* 48 */       }); this.oidcUserHandler = (user -> this.oauth2UserHandler.accept(user));
/*    */   }
/*    */   
/*    */   public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
/* 52 */     if (authentication instanceof org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken) {
/* 53 */       if (authentication.getPrincipal() instanceof OidcUser) {
/* 54 */         this.oidcUserHandler.accept((OidcUser)authentication.getPrincipal());
/* 55 */       } else if (authentication.getPrincipal() instanceof OAuth2User) {
/* 56 */         this.oauth2UserHandler.accept((OAuth2User)authentication.getPrincipal());
/*    */       } 
/*    */     }
/*    */     
/* 60 */     this.delegate.onAuthenticationSuccess(request, response, authentication);
/*    */   }
/*    */   
/*    */   public void setOAuth2UserHandler(Consumer<OAuth2User> oauth2UserHandler) {
/* 64 */     this.oauth2UserHandler = oauth2UserHandler;
/*    */   }
/*    */   
/*    */   public void setOidcUserHandler(Consumer<OidcUser> oidcUserHandler) {
/* 68 */     this.oidcUserHandler = oidcUserHandler;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\federation\FederatedIdentityAuthenticationSuccessHandler.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */