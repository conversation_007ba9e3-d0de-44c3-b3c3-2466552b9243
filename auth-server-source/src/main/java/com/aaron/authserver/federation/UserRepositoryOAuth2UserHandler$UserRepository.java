/*    */ package com.aaron.authserver.federation;
/*    */ 
/*    */ import com.aaron.authserver.federation.UserRepositoryOAuth2UserHandler;
/*    */ import java.util.Map;
/*    */ import java.util.concurrent.ConcurrentHashMap;
/*    */ import org.springframework.security.oauth2.core.user.OAuth2User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class UserRepository
/*    */ {
   private final Map<String, OAuth2User> userCache = new ConcurrentHashMap<>();
/*    */   
/*    */   public OAuth2User findByName(String name) {
     return this.userCache.get(name);
/*    */   }
/*    */   
/*    */   public void save(OAuth2User oauth2User) {
     this.userCache.put(oauth2User.getName(), oauth2User);
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\federation\UserRepositoryOAuth2UserHandler$UserRepository.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
