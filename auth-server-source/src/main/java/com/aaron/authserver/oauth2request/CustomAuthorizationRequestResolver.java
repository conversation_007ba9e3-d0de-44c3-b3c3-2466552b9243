/*    */ package BOOT-INF.classes.com.aaron.authserver.oauth2request;
/*    */ 
/*    */ import jakarta.servlet.http.HttpServletRequest;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
/*    */ import org.springframework.security.oauth2.client.web.DefaultOAuth2AuthorizationRequestResolver;
/*    */ import org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestResolver;
/*    */ import org.springframework.security.oauth2.core.endpoint.OAuth2AuthorizationRequest;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CustomAuthorizationRequestResolver
/*    */   implements OAuth2AuthorizationRequestResolver
/*    */ {
/*    */   private OAuth2AuthorizationRequestResolver defaultResolver;
/*    */   
/*    */   public CustomAuthorizationRequestResolver(ClientRegistrationRepository repo, String authorizationRequestBaseUri) {
/* 23 */     this.defaultResolver = (OAuth2AuthorizationRequestResolver)new DefaultOAuth2AuthorizationRequestResolver(repo, authorizationRequestBaseUri);
/*    */   }
/*    */ 
/*    */   
/*    */   public OAuth2AuthorizationRequest resolve(HttpServletRequest request) {
/* 28 */     OAuth2AuthorizationRequest req = this.defaultResolver.resolve(request);
/* 29 */     if (req != null) {
/* 30 */       req = customizeAuthorizationRequest(req);
/*    */     }
/* 32 */     return req;
/*    */   }
/*    */ 
/*    */   
/*    */   public OAuth2AuthorizationRequest resolve(HttpServletRequest request, String clientRegistrationId) {
/* 37 */     OAuth2AuthorizationRequest req = this.defaultResolver.resolve(request, clientRegistrationId);
/* 38 */     if (req != null) {
/* 39 */       req = customizeAuthorizationRequest(req);
/*    */     }
/* 41 */     return req;
/*    */   }
/*    */   
/*    */   private OAuth2AuthorizationRequest customizeAuthorizationRequest(OAuth2AuthorizationRequest req) {
/* 45 */     Map<String, Object> extraParams = new HashMap<>();
/* 46 */     extraParams.putAll(req.getAdditionalParameters());
/* 47 */     extraParams.put("test", "extra");
/* 48 */     System.out.println("here =====================");
/* 49 */     return OAuth2AuthorizationRequest.from(req).additionalParameters(extraParams).build();
/*    */   }
/*    */   
/*    */   private OAuth2AuthorizationRequest customizeAuthorizationRequest1(OAuth2AuthorizationRequest req) {
/* 53 */     return OAuth2AuthorizationRequest.from(req).state("xyz").build();
/*    */   }
/*    */   
/*    */   private OAuth2AuthorizationRequest customizeOktaReq(OAuth2AuthorizationRequest req) {
/* 57 */     Map<String, Object> extraParams = new HashMap<>();
/* 58 */     extraParams.putAll(req.getAdditionalParameters());
/* 59 */     extraParams.put("idp", "https://idprovider.com");
/* 60 */     return OAuth2AuthorizationRequest.from(req).additionalParameters(extraParams).build();
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\oauth2request\CustomAuthorizationRequestResolver.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */