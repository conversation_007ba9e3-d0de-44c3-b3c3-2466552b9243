/*    */ package BOOT-INF.classes.com.aaron.authserver;
/*    */ 
/*    */ import com.aaron.authserver.AuthServerApplication;
/*    */ import java.util.function.Supplier;
/*    */ import org.springframework.beans.factory.config.BeanDefinition;
/*    */ import org.springframework.beans.factory.support.RootBeanDefinition;
/*    */ import org.springframework.context.annotation.ConfigurationClassUtils;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AuthServerApplication__BeanDefinitions
/*    */ {
/*    */   public static BeanDefinition getAuthServerApplicationBeanDefinition() {
/* 15 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(AuthServerApplication.class);
/* 16 */     rootBeanDefinition.setTargetType(AuthServerApplication.class);
/* 17 */     ConfigurationClassUtils.initializeConfigurationClass(AuthServerApplication.class);
/* 18 */     rootBeanDefinition.setInstanceSupplier(null::new);
/* 19 */     return (BeanDefinition)rootBeanDefinition;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\AuthServerApplication__BeanDefinitions.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */