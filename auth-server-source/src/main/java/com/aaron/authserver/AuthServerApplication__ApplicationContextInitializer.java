/*    */ package BOOT-INF.classes.com.aaron.authserver;
/*    */ 
/*    */ import com.aaron.authserver.AuthServerApplication__BeanFactoryRegistrations;
/*    */ import java.util.Comparator;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import org.springframework.beans.factory.config.BeanDefinition;
/*    */ import org.springframework.beans.factory.support.AutowireCandidateResolver;
/*    */ import org.springframework.beans.factory.support.DefaultListableBeanFactory;
/*    */ import org.springframework.beans.factory.support.RootBeanDefinition;
/*    */ import org.springframework.context.ApplicationContextInitializer;
/*    */ import org.springframework.context.ConfigurableApplicationContext;
/*    */ import org.springframework.context.annotation.ContextAnnotationAutowireCandidateResolver;
/*    */ import org.springframework.context.annotation.ImportAwareAotBeanPostProcessor;
/*    */ import org.springframework.context.support.GenericApplicationContext;
/*    */ import org.springframework.core.annotation.AnnotationAwareOrderComparator;
/*    */ 
/*    */ public class AuthServerApplication__ApplicationContextInitializer
/*    */   implements ApplicationContextInitializer<GenericApplicationContext>
/*    */ {
/*    */   public void initialize(GenericApplicationContext paramGenericApplicationContext) {
/* 22 */     DefaultListableBeanFactory defaultListableBeanFactory = paramGenericApplicationContext.getDefaultListableBeanFactory();
/* 23 */     defaultListableBeanFactory.setAutowireCandidateResolver((AutowireCandidateResolver)new ContextAnnotationAutowireCandidateResolver());
/* 24 */     defaultListableBeanFactory.setDependencyComparator((Comparator)AnnotationAwareOrderComparator.INSTANCE);
/* 25 */     paramGenericApplicationContext.getEnvironment().addActiveProfile("test");
/* 26 */     addImportAwareBeanPostProcessors(defaultListableBeanFactory);
/* 27 */     (new AuthServerApplication__BeanFactoryRegistrations()).registerBeanDefinitions(defaultListableBeanFactory);
/* 28 */     (new AuthServerApplication__BeanFactoryRegistrations()).registerAliases(defaultListableBeanFactory);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void addImportAwareBeanPostProcessors(DefaultListableBeanFactory paramDefaultListableBeanFactory) {
/* 35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 36 */     hashMap.put("org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration", "org.springframework.boot.autoconfigure.security.servlet.SpringBootWebSecurityConfiguration$WebSecurityEnablerConfiguration");
/* 37 */     hashMap.put("org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration", "org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration");
/* 38 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(ImportAwareAotBeanPostProcessor.class);
/* 39 */     rootBeanDefinition.setRole(2);
/* 40 */     rootBeanDefinition.setInstanceSupplier(() -> new ImportAwareAotBeanPostProcessor(paramMap));
/* 41 */     paramDefaultListableBeanFactory.registerBeanDefinition("org.springframework.context.annotation.internalImportAwareAotProcessor", (BeanDefinition)rootBeanDefinition);
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\AuthServerApplication__ApplicationContextInitializer.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */