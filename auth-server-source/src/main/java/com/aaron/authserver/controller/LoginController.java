/*    */ package BOOT-INF.classes.com.aaron.authserver.controller;
/*    */ 
/*    */ import org.springframework.stereotype.Controller;
/*    */ import org.springframework.web.bind.annotation.GetMapping;
/*    */ 
/*    */ @Controller
/*    */ public class LoginController
/*    */ {
/*    */   @GetMapping({"/login"})
/*    */   public String login() {
/* 11 */     return "login";
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\controller\LoginController.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */