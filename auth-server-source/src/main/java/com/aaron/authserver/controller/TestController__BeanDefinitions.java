/*    */ package BOOT-INF.classes.com.aaron.authserver.controller;
/*    */ 
/*    */ import com.aaron.authserver.controller.TestController;
/*    */ import java.util.function.Supplier;
/*    */ import org.springframework.beans.factory.config.BeanDefinition;
/*    */ import org.springframework.beans.factory.support.RootBeanDefinition;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TestController__BeanDefinitions
/*    */ {
/*    */   public static BeanDefinition getTestControllerBeanDefinition() {
/* 14 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(TestController.class);
/* 15 */     rootBeanDefinition.setInstanceSupplier(TestController::new);
/* 16 */     return (BeanDefinition)rootBeanDefinition;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\controller\TestController__BeanDefinitions.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */