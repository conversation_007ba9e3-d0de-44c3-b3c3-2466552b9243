/*    */ package BOOT-INF.classes.com.aaron.authserver.controller;
/*    */ 
/*    */ import org.springframework.security.access.prepost.PreAuthorize;
/*    */ import org.springframework.web.bind.annotation.CrossOrigin;
/*    */ import org.springframework.web.bind.annotation.GetMapping;
/*    */ import org.springframework.web.bind.annotation.RequestMapping;
/*    */ import org.springframework.web.bind.annotation.RestController;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @CrossOrigin(origins = {"*"}, maxAge = 3600L)
/*    */ @RestController
/*    */ @RequestMapping({"/api/test"})
/*    */ public class TestController
/*    */ {
/*    */   @GetMapping({"/all"})
/*    */   public String allAccess() {
/* 19 */     return "Public Content.";
/*    */   }
/*    */   
/*    */   @GetMapping({"/user"})
/*    */   @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
/*    */   public String userAccess() {
/* 25 */     return "User Content.";
/*    */   }
/*    */   
/*    */   @GetMapping({"/mod"})
/*    */   @PreAuthorize("hasRole('MODERATOR')")
/*    */   public String moderatorAccess() {
/* 31 */     return "Moderator Board.";
/*    */   }
/*    */   
/*    */   @GetMapping({"/admin"})
/*    */   @PreAuthorize("hasRole('ADMIN')")
/*    */   public String adminAccess() {
/* 37 */     return "Admin Board.";
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\controller\TestController.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */