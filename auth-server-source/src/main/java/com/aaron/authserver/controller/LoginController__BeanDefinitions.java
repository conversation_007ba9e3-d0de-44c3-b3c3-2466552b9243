/*    */ package BOOT-INF.classes.com.aaron.authserver.controller;
/*    */ 
/*    */ import com.aaron.authserver.controller.LoginController;
/*    */ import java.util.function.Supplier;
/*    */ import org.springframework.beans.factory.config.BeanDefinition;
/*    */ import org.springframework.beans.factory.support.RootBeanDefinition;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LoginController__BeanDefinitions
/*    */ {
/*    */   public static BeanDefinition getLoginControllerBeanDefinition() {
/* 14 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(LoginController.class);
/* 15 */     rootBeanDefinition.setInstanceSupplier(LoginController::new);
/* 16 */     return (BeanDefinition)rootBeanDefinition;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\controller\LoginController__BeanDefinitions.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */