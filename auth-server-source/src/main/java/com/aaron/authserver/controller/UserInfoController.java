/*    */ package com.aaron.authserver.controller;
/*    */ 
/*    */ import java.security.Principal;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import org.springframework.security.core.annotation.AuthenticationPrincipal;
/*    */ import org.springframework.security.core.context.SecurityContextHolder;
/*    */ import org.springframework.web.bind.annotation.GetMapping;
/*    */ import org.springframework.web.bind.annotation.RequestMapping;
/*    */ import org.springframework.web.bind.annotation.RestController;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @RestController("RestBlogControllerV4")
/*    */ @RequestMapping({"/user"})
/*    */ public class UserInfoController
/*    */ {
/*    */   @GetMapping({"/userinfo"})
/*    */   public Map<String, Object> user(@AuthenticationPrincipal Principal principal) {
     if (principal != null) {
       return Map.of("name", principal.getName(), "authorities", SecurityContextHolder.getContext().getAuthentication().getAuthorities());
/*    */     }
     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @GetMapping({"/userinfo2"})
/*    */   public Map<String, Object> user2(@AuthenticationPrincipal Principal principal) {
     Map<String, Object> map = new HashMap<>();
     map.put("given_name", "Demo First Name");
     map.put("family_name", "Demo Last Name");
     map.put("name", "demo");
     map.put("sub", "demo");
     return map;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\controller\UserInfoController.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */
