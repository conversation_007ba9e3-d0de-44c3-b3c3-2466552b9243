/*    */ package BOOT-INF.classes.com.aaron.authserver.controller;
/*    */ 
/*    */ import java.security.Principal;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import org.springframework.security.core.annotation.AuthenticationPrincipal;
/*    */ import org.springframework.security.core.context.SecurityContextHolder;
/*    */ import org.springframework.web.bind.annotation.GetMapping;
/*    */ import org.springframework.web.bind.annotation.RequestMapping;
/*    */ import org.springframework.web.bind.annotation.RestController;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @RestController("RestBlogControllerV4")
/*    */ @RequestMapping({"/user"})
/*    */ public class UserInfoController
/*    */ {
/*    */   @GetMapping({"/userinfo"})
/*    */   public Map<String, Object> user(@AuthenticationPrincipal Principal principal) {
/* 25 */     if (principal != null) {
/* 26 */       return Map.of("name", principal.getName(), "authorities", SecurityContextHolder.getContext().getAuthentication().getAuthorities());
/*    */     }
/* 28 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @GetMapping({"/userinfo2"})
/*    */   public Map<String, Object> user2(@AuthenticationPrincipal Principal principal) {
/* 45 */     Map<String, Object> map = new HashMap<>();
/* 46 */     map.put("given_name", "Demo First Name");
/* 47 */     map.put("family_name", "Demo Last Name");
/* 48 */     map.put("name", "demo");
/* 49 */     map.put("sub", "demo");
/* 50 */     return map;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\controller\UserInfoController.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */