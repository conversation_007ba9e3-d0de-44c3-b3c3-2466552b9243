/*    */ package BOOT-INF.classes.com.aaron.authserver.controller;
/*    */ 
/*    */ import com.aaron.authserver.model.Authorities;
/*    */ import com.aaron.authserver.model.User;
/*    */ import com.aaron.authserver.model.UserTest;
/*    */ import com.aaron.authserver.model.User_Authority;
/*    */ import com.aaron.authserver.service.UserService;
/*    */ import java.util.List;
/*    */ import java.util.Optional;
/*    */ import org.springframework.http.HttpStatus;
/*    */ import org.springframework.web.bind.annotation.DeleteMapping;
/*    */ import org.springframework.web.bind.annotation.GetMapping;
/*    */ import org.springframework.web.bind.annotation.PathVariable;
/*    */ import org.springframework.web.bind.annotation.PostMapping;
/*    */ import org.springframework.web.bind.annotation.PutMapping;
/*    */ import org.springframework.web.bind.annotation.RequestBody;
/*    */ import org.springframework.web.bind.annotation.ResponseStatus;
/*    */ import org.springframework.web.bind.annotation.RestController;
/*    */ 
/*    */ 
/*    */ @RestController
/*    */ public class UserController
/*    */ {
/*    */   private final UserService userService;
/*    */   
/*    */   public UserController(UserService userService) {
/* 27 */     this.userService = userService;
/*    */   }
/*    */   
/*    */   @GetMapping({"/user"})
/*    */   public List<User> getAllUsers() {
/* 32 */     return this.userService.findAllUsers();
/*    */   }
/*    */   
/*    */   @GetMapping({"/user_rowmapper"})
/*    */   public List<User> getAllUsersRowMapper() {
/* 37 */     return this.userService.findAllUsersRowMapper();
/*    */   }
/*    */   
/*    */   @GetMapping({"/user_test"})
/*    */   public List<UserTest> getAllUsersTest() {
/* 42 */     return this.userService.findAllUsersTest();
/*    */   }
/*    */   
/*    */   @GetMapping({"/user_test_rowmapper"})
/*    */   public List<UserTest> getAllUsersTestRowMapper() {
/* 47 */     return this.userService.findAllUsersTestRowMapper();
/*    */   }
/*    */ 
/*    */   
/*    */   @GetMapping({"/users_authorities"})
/*    */   public List<User_Authority> getAllUsersAuthorities() {
/* 53 */     return this.userService.findAllUsersAuthorities();
/*    */   }
/*    */   
/*    */   @GetMapping({"/authorities_users"})
/*    */   public List<Authorities> getAllAuthoritiesUsers() {
/* 58 */     return this.userService.findAllAuthoritiesUsers();
/*    */   }
/*    */   
/*    */   @GetMapping({"/user/{username}"})
/*    */   public Optional<User> findByUserName(@PathVariable(name = "username") String username) {
/* 63 */     return this.userService.findByUsername(username);
/*    */   }
/*    */   
/*    */   @PostMapping({"/user"})
/*    */   @ResponseStatus(HttpStatus.CREATED)
/*    */   public void createUser(@RequestBody User user) {
/* 69 */     this.userService.createUser(user);
/*    */   }
/*    */   
/*    */   @PutMapping({"/user"})
/*    */   public void updateUser(@RequestBody User user) {
/* 74 */     this.userService.updateUser(user);
/*    */   }
/*    */   
/*    */   @DeleteMapping({"/user/{username}"})
/*    */   public void deleteUser(@PathVariable(name = "username") String username) {
/* 79 */     this.userService.deleteUser(username);
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\controller\UserController.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */