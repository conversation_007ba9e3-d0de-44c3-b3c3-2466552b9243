/*    */ package BOOT-INF.classes.com.aaron.authserver.controller;
/*    */ 
/*    */ import com.aaron.authserver.controller.UserController;
/*    */ import com.aaron.authserver.service.UserService;
/*    */ import java.util.function.Supplier;
/*    */ import org.springframework.beans.factory.aot.AutowiredArguments;
/*    */ import org.springframework.beans.factory.aot.BeanInstanceSupplier;
/*    */ import org.springframework.beans.factory.config.BeanDefinition;
/*    */ import org.springframework.beans.factory.support.RegisteredBean;
/*    */ import org.springframework.beans.factory.support.RootBeanDefinition;
/*    */ 
/*    */ 
/*    */ public class UserController__BeanDefinitions
/*    */ {
/*    */   private static BeanInstanceSupplier<UserController> getUserControllerInstanceSupplier() {
/* 16 */     return BeanInstanceSupplier.forConstructor(new Class[] { UserService.class
/* 17 */         }).withGenerator((paramRegisteredBean, paramAutowiredArguments) -> new UserController((UserService)paramAutowiredArguments.get(0)));
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static BeanDefinition getUserControllerBeanDefinition() {
/* 24 */     RootBeanDefinition rootBeanDefinition = new RootBeanDefinition(UserController.class);
/* 25 */     rootBeanDefinition.setInstanceSupplier((Supplier)getUserControllerInstanceSupplier());
/* 26 */     return (BeanDefinition)rootBeanDefinition;
/*    */   }
/*    */ }


/* Location:              D:\tmp\auth-server.jar!\BOOT-INF\classes\com\aaron\authserver\controller\UserController__BeanDefinitions.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */