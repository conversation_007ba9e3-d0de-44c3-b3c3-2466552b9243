# Auth Server - OAuth2 Authorization Server

这是一个基于Spring Boot 3.2.0的OAuth2授权服务器项目，从JAR文件还原而来。

## 项目信息

- **项目名称**: auth-server
- **Group ID**: com.aaron
- **Artifact ID**: auth-server
- **版本**: 0.0.1-SNAPSHOT
- **Java版本**: 17
- **Spring Boot版本**: 3.2.0

## 技术栈

- Spring Boot 3.2.0
- Spring Security OAuth2 Authorization Server
- Spring Security OAuth2 Client
- Spring Boot Thymeleaf
- Spring Boot JDBC
- MySQL 5.1.47
- Apache Commons Lang3 & Text

## 项目结构

```
src/
├── main/
│   ├── java/com/aaron/authserver/
│   │   ├── AuthServerApplication.java          # 主启动类
│   │   ├── authentication/                     # 认证相关
│   │   │   └── dao/                           # 数据访问对象
│   │   ├── config/                            # 配置类
│   │   │   ├── AuthorizationServerConfig.java # 授权服务器配置
│   │   │   ├── DefaultSecurityConfig.java     # 默认安全配置
│   │   │   └── SecurityConfig.java            # 安全配置
│   │   ├── controller/                        # 控制器
│   │   │   ├── AuthController.java            # 认证控制器
│   │   │   ├── LoginController.java           # 登录控制器
│   │   │   ├── TestController.java            # 测试控制器
│   │   │   ├── UserController.java            # 用户控制器
│   │   │   └── UserInfoController.java        # 用户信息控制器
│   │   ├── federation/                        # 联邦身份验证
│   │   ├── mapper/                            # 数据映射器
│   │   ├── model/                             # 数据模型
│   │   ├── oauth2request/                     # OAuth2请求处理
│   │   └── service/                           # 服务层
│   └── resources/
│       ├── application.yml                    # 主配置文件
│       ├── application-dev.yml                # 开发环境配置
│       ├── application-test.yml               # 测试环境配置
│       ├── application-prod.yml               # 生产环境配置
│       ├── logback.xml                        # 日志配置
│       ├── static/                            # 静态资源
│       └── templates/                         # Thymeleaf模板
└── test/
    └── java/                                  # 测试代码
```

## 数据库配置

项目使用MySQL数据库，默认配置：
- 数据库: ebook
- 用户名: root
- 密码: hello123
- 端口: 3306

## OAuth2配置

- 客户端ID: client
- 客户端密钥: secret
- 授权类型: authorization_code, refresh_token, client_credentials
- 作用域: openid, profile, user.read, user.write

## 运行说明

1. 确保MySQL数据库运行并创建了相应的数据库
2. 根据需要修改配置文件中的数据库连接信息
3. 使用Maven构建项目: `mvn clean install`
4. 运行应用: `mvn spring-boot:run` 或直接运行主类
5. 访问: http://localhost:9000

## 注意事项

- 本项目是从JAR文件还原而来，Java源代码需要通过反编译工具生成
- 配置文件和资源文件已经完整复制
- 需要根据实际需求调整数据库配置和OAuth2客户端配置
- 建议在还原源代码后进行充分的测试

## 下一步

1. 使用JD-GUI反编译所有.class文件生成Java源代码
2. 将反编译后的源代码放入对应的包目录中
3. 检查和修复可能的反编译问题
4. 运行测试确保功能正常
