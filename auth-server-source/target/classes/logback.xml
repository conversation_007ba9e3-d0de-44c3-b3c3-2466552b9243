<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <jmxConfigurator />
    <!--<property resource="logback.properties" />-->
    <!--取消初始化的信息-->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />
    <!-- 日志输出格式 -->
    <contextName>eBook</contextName>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{80}:%L - %msg%n</pattern>
        </encoder>
        <!--<target>System.out</target>-->
    </appender>

    <appender name="rollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logback.file.path}/ebook-sys.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <fileNamePattern>${logback.file.path}/%d{yyyy-MM}/ebook-sys-%d{yyyy-MM-dd_HH}.%i.log</fileNamePattern>
            <!--日志文件保留天数-->
            <maxHistory>${logback.maxHistory}</maxHistory>
            <!-- 日志文件最大尺寸 -->
            <maxFileSize>${logback.maxFileSize}</maxFileSize>
            <!--<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>5MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>-->
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{80}:%L - %msg%n</pattern>
            </layout>
        </encoder>
        <!--日志文件最大的大小-->
<!--        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">-->
<!--            <MaxFileSize>10MB</MaxFileSize>-->
<!--        </triggeringPolicy>-->
    </appender>

    <logger name="java.sql" level="${logback.level}"/>
    <logger name="com.aaron" level="${logback.level}" />
    <logger name="org.springframework.data.mongodb.core" level="${logback.level}"/>
    <logger name="org.apache.zookeeper" level="OFF"/>
    <logger name="org.apache.curator" level="OFF"/>
    <root level="ERROR">
        <appender-ref ref="${logback.appenderRef}" />
    </root>
</configuration>