# 反编译指南

## 使用JD-GUI反编译Java类文件

### 步骤1: 启动JD-GUI
运行项目根目录下的 `decompile.bat` 文件，或者直接运行：
```
D:\DEV\JAVA\jd-gui-0.3.5.windows\jd-gui.exe
```

### 步骤2: 打开类文件目录
在JD-GUI中：
1. 点击 File -> Open Directory
2. 选择 `BOOT-INF/classes` 目录
3. 展开 `com/aaron/authserver` 包

### 步骤3: 需要反编译的主要类文件

#### 主启动类
- `AuthServerApplication.class` -> `AuthServerApplication.java`

#### 配置类 (config包)
- `AuthorizationServerConfig.class` -> `AuthorizationServerConfig.java`
- `DefaultSecurityConfig.class` -> `DefaultSecurityConfig.java`
- `SecurityConfig.class` -> `SecurityConfig.java`

#### 控制器类 (controller包)
- `AuthController.class` -> `AuthController.java`
- `LoginController.class` -> `LoginController.java`
- `TestController.class` -> `TestController.java`
- `UserController.class` -> `UserController.java`
- `UserInfoController.class` -> `UserInfoController.java`

#### 模型类 (model包)
- `User.class` -> `User.java`
- `Authorities.class` -> `Authorities.java`
- `InspirataOAuth2User.class` -> `InspirataOAuth2User.java`
- `User_Authority.class` -> `User_Authority.java`
- `UserTest.class` -> `UserTest.java`
- `AclMode.class` -> `AclMode.java`
- `CrudType.class` -> `CrudType.java`
- `IDataPermission.class` -> `IDataPermission.java`
- `IRole.class` -> `IRole.java`
- `ITable.class` -> `ITable.java`
- `IUser.class` -> `IUser.java`

#### 服务类 (service包)
- `UserService.class` -> `UserService.java`

#### 数据映射器 (mapper包)
- `AuthoritiesResultExtractor.class` -> `AuthoritiesResultExtractor.java`
- `UserResultExtractor.class` -> `UserResultExtractor.java`
- `UserRowMapper.class` -> `UserRowMapper.java`
- `UserTestRowMapper.class` -> `UserTestRowMapper.java`

#### 联邦身份验证 (federation包)
- `FederatedIdentityAuthenticationSuccessHandler.class` -> `FederatedIdentityAuthenticationSuccessHandler.java`
- `FederatedIdentityIdTokenCustomizer.class` -> `FederatedIdentityIdTokenCustomizer.java`
- `UserRepositoryOAuth2UserHandler.class` -> `UserRepositoryOAuth2UserHandler.java`

#### OAuth2请求处理 (oauth2request包)
- `CustomAuthorizationRequestResolver.class` -> `CustomAuthorizationRequestResolver.java`

#### 认证相关 (authentication/dao包)
- 检查是否有相关的DAO类文件

### 步骤4: 保存反编译后的源代码
1. 在JD-GUI中选择要保存的类
2. 点击 File -> Save All Sources
3. 选择保存到 `auth-server-source/src/main/java` 目录
4. 确保保持包结构完整

### 步骤5: 注意事项
1. **跳过Spring生成的类**: 忽略包含 `$$SpringCGLIB$$` 或 `__BeanDefinitions` 等后缀的类
2. **检查包声明**: 确保每个Java文件的包声明正确
3. **导入语句**: 检查import语句是否完整
4. **泛型信息**: 反编译可能丢失部分泛型信息，需要手动修复
5. **注解信息**: 检查Spring注解是否完整

### 步骤6: 后续处理
反编译完成后：
1. 检查所有Java文件是否在正确的包目录中
2. 尝试编译项目: `mvn clean compile`
3. 修复编译错误
4. 运行测试确保功能正常

### 常见问题处理
1. **编译错误**: 通常是由于反编译器无法完美还原某些语法结构
2. **缺失注解**: 手动添加Spring相关注解如@RestController, @Service等
3. **泛型擦除**: 根据上下文推断并添加泛型类型
4. **Lambda表达式**: 可能需要手动优化反编译后的匿名类
