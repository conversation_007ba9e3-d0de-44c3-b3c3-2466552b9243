# PowerShell script to fix malformed first lines in Java files

$sourceDir = "src\main\java"

# Get all Java files recursively
$javaFiles = Get-ChildItem -Path $sourceDir -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    Write-Host "Processing: $($file.Name)"
    
    try {
        # Read the file content
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        
        # Fix malformed package declarations
        $content = $content -replace "^\*\s*\*/\s*package\s", "package "
        $content = $content -replace "^\*/\s*package\s", "package "
        $content = $content -replace "^\*\s*package\s", "package "
        
        # Fix other malformed first lines
        $content = $content -replace "^\*\s*\*/\s*import\s", "import "
        $content = $content -replace "^\*/\s*import\s", "import "
        $content = $content -replace "^\*\s*import\s", "import "
        
        $content = $content -replace "^\*\s*\*/\s*public\s", "public "
        $content = $content -replace "^\*/\s*public\s", "public "
        $content = $content -replace "^\*\s*public\s", "public "
        
        # Remove BOM if still present
        if ($content.StartsWith([char]0xFEFF)) {
            $content = $content.Substring(1)
        }
        
        # Write back the corrected content
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBom)
        
        Write-Host "  Fixed malformed lines for: $($file.Name)"
    }
    catch {
        Write-Host "  Error processing $($file.Name): $($_.Exception.Message)"
    }
}

Write-Host "Malformed lines fix completed!"
