# PowerShell script to fix BOM characters in Java files

$sourceDir = "src\main\java\com\aaron\authserver"

# Get all Java files recursively
$javaFiles = Get-ChildItem -Path $sourceDir -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    Write-Host "Processing: $($file.Name)"
    
    # Read the file content as bytes
    $bytes = [System.IO.File]::ReadAllBytes($file.FullName)
    
    # Check if file starts with BOM (EF BB BF)
    if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
        Write-Host "  Removing BOM from: $($file.Name)"
        
        # Remove BOM by skipping first 3 bytes
        $newBytes = $bytes[3..($bytes.Length-1)]
        
        # Write back without BOM
        [System.IO.File]::WriteAllBytes($file.FullName, $newBytes)
    }
}

Write-Host "BOM fix completed!"
