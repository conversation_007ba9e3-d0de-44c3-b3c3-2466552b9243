
server:
  port: 9000

logging:
  level:
    org:
      springframework:
        security: INFO

spring:
  application:
    name: authserver
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: ****************************************************************************************************************************************************************************************************************************
    username: endao_bookstore
    password: sxBPQMFnx2U@e8
    hikari:
      maximum-pool-size: 5
      max-lifetime: 3600000
      idle-timeout: 600000
  messages:
    basename: static/i18n/messages
    encoding: UTF-8
    fallback-to-system-locale: true
  security:
    oauth2:
      authorizationserver:
        client:
          client:
            registration:
              client-id: "client"
              client-name: "恩道书房"
              client-secret: "8e7d8418f3f446db31d4612fb14692076ac1ed7b"
              client-authentication-methods:
                - "client_secret_basic"
              authorization-grant-types:
                - "authorization_code"
                - "refresh_token"
                - "client_credentials"
              redirect-uris:
                - "http://20.189.113.131" #http://20.189.113.131 #http://20.189.113.131/login/oauth2/code/oidc-client
                - "http://20.189.113.131/?auth=sso"
                - "http://20.189.113.131/wp-admin/admin-ajax.php?action=openid-connect-authorize"
                - "http://20.189.113.131/wp-admin/admin-ajax.php?action=openid-connect-authorize&continue"
                - "https://betabook.endao.co" #miniOrange
                - "https://book.endao.co"
                - "https://edbooks.cc"
              post-logout-redirect-uris:
                - "http://test" #"http://127.0.0.1:8080/logged-out"
              scopes:
                - "openid"
                - "profile"
                - "user.read"
                - "user.write"
            require-authorization-consent: false #是否启用consent
            require-proof-key: false #是否启用PCKE
            token:
              authorization-code-time-to-live: 3600000
              access-token-time-to-live: 3600000 #毫秒 60分钟


UserController:
  signin: Authenticates user and returns its JWT token.
  signup: Creates user and returns its JWT token
  delete: Deletes specific user by username
  search: Returns specific user by username
  me: Returns current user's data