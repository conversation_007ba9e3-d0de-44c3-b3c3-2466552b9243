# PowerShell script to fix truncated "package" keywords in Java files

$sourceDir = "src\main\java"

# Get all Java files recursively
$javaFiles = Get-ChildItem -Path $sourceDir -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    Write-Host "Processing: $($file.Name)"
    
    try {
        # Read the file content
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        
        # Fix truncated package declaration
        if ($content -match "^ackage\s") {
            $content = $content -replace "^ackage\s", "package "
            Write-Host "  Fixed truncated 'package' keyword"
        }
        
        # Fix other potential truncated keywords
        $content = $content -replace "^mport\s", "import "
        $content = $content -replace "^ublic\s", "public "
        $content = $content -replace "^rivate\s", "private "
        $content = $content -replace "^rotected\s", "protected "
        $content = $content -replace "^lass\s", "class "
        $content = $content -replace "^nterface\s", "interface "
        $content = $content -replace "^num\s", "enum "
        
        # Write back the corrected content
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBom)
        
        Write-Host "  Fixed keywords for: $($file.Name)"
    }
    catch {
        Write-Host "  Error processing $($file.Name): $($_.Exception.Message)"
    }
}

Write-Host "Package keyword fix completed!"
